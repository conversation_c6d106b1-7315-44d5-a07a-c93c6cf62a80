<?php
/**
 * View Support Data
 *
 * This file displays the support ticket data.
 *
 * @package LendSwift
 */

// Define LENDSWIFT constant to prevent direct access to included files
define('LENDSWIFT', true);

// Include initialization file
require_once '../includes/init.php';

// Check if the admin is logged in
if (!is_admin_logged_in()) {
    set_flash_message('error', 'Please log in to access the admin dashboard.');
    redirect(BASE_URL . '/admin/login.php');
}

// Get database connection
$db = getDbConnection();

// Get support tickets
$tickets = [];
try {
    $result = $db->query("SELECT * FROM support_tickets");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $tickets[] = $row;
        }
    }
} catch (Exception $e) {
    $error_message = "Error fetching tickets: " . $e->getMessage();
}

// Get support messages
$messages = [];
try {
    $result = $db->query("SELECT * FROM support_messages");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $messages[] = $row;
        }
    }
} catch (Exception $e) {
    $error_message = "Error fetching messages: " . $e->getMessage();
}

// Output result
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Support Data</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
        }
        h1, h2 {
            color: #333;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .error {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>View Support Data</h1>
    
    <?php if (isset($error_message)): ?>
        <p class="error"><?php echo $error_message; ?></p>
    <?php endif; ?>
    
    <h2>Support Tickets</h2>
    <?php if (empty($tickets)): ?>
        <p>No support tickets found.</p>
    <?php else: ?>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>User ID</th>
                    <th>Subject</th>
                    <th>Priority</th>
                    <th>Status</th>
                    <th>Created At</th>
                    <th>Updated At</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($tickets as $ticket): ?>
                    <tr>
                        <td><?php echo $ticket['id']; ?></td>
                        <td><?php echo $ticket['user_id']; ?></td>
                        <td><?php echo htmlspecialchars($ticket['subject']); ?></td>
                        <td><?php echo $ticket['priority']; ?></td>
                        <td><?php echo $ticket['status']; ?></td>
                        <td><?php echo $ticket['created_at']; ?></td>
                        <td><?php echo $ticket['updated_at']; ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php endif; ?>
    
    <h2>Support Messages</h2>
    <?php if (empty($messages)): ?>
        <p>No support messages found.</p>
    <?php else: ?>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Ticket ID</th>
                    <th>Sender Type</th>
                    <th>Sender ID</th>
                    <th>Message</th>
                    <th>Created At</th>
                    <th>Is Read</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($messages as $message): ?>
                    <tr>
                        <td><?php echo $message['id']; ?></td>
                        <td><?php echo $message['ticket_id']; ?></td>
                        <td><?php echo $message['sender_type']; ?></td>
                        <td><?php echo $message['sender_id']; ?></td>
                        <td><?php echo htmlspecialchars($message['message']); ?></td>
                        <td><?php echo $message['created_at']; ?></td>
                        <td><?php echo $message['is_read'] ? 'Yes' : 'No'; ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php endif; ?>
    
    <p><a href="<?php echo BASE_URL; ?>/admin/index.php">Back to Dashboard</a></p>
</body>
</html>
