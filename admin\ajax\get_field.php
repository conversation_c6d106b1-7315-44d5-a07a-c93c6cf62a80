<?php
/**
 * AJ<PERSON><PERSON> Handler for Getting Field Data
 *
 * This file handles AJAX requests to get field data for the form builder.
 *
 * @package LendSwift
 */

// Define LENDSWIFT constant to prevent direct access to included files
define('LENDSWIFT', true);

// Include initialization file
require_once '../../includes/init.php';

// Check if the admin is logged in
if (!is_admin_logged_in()) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Check if field ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Field ID is required']);
    exit;
}

$field_id = (int)$_GET['id'];

// Get database connection
$db = getDbConnection();

// Get field data
$stmt = $db->prepare("
    SELECT id, form_id, label, name, type, placeholder, help_text, options, is_required, display_order
    FROM form_fields
    WHERE id = ?
");
$stmt->bind_param("i", $field_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result && $result->num_rows > 0) {
    $field = $result->fetch_assoc();
    
    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'field' => $field]);
} else {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Field not found']);
}