<?php
/**
 * Send Test Email AJAX Endpoint
 * 
 * This file handles sending a test email using the configured SMTP settings.
 * 
 * @package LendSwift
 */

// Define LENDSWIFT constant to prevent direct access to included files
if (!defined('LENDSWIFT')) {
    define('LENDSWIFT', true);
}

// Include initialization file
require_once '../../includes/init.php';

// Check if the admin is logged in
if (!is_admin_logged_in()) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access.'
    ]);
    exit;
}

// Check if this is an AJAX request
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) !== 'xmlhttprequest') {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request.'
    ]);
    exit;
}

// Get the email address from the request
$email = $_POST['email'] ?? '';

// Validate email
if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Please enter a valid email address.'
    ]);
    exit;
}

// Get SMTP settings
$use_smtp = $_POST['use_smtp'] ?? get_setting('use_smtp', '0');
$smtp_host = $_POST['smtp_host'] ?? get_setting('smtp_host', 'smtp.hostinger.com');
$smtp_port = $_POST['smtp_port'] ?? get_setting('smtp_port', '465');
$smtp_secure = $_POST['smtp_secure'] ?? get_setting('smtp_secure', 'ssl');
$smtp_username = $_POST['smtp_username'] ?? get_setting('smtp_username', '<EMAIL>');
$smtp_password = $_POST['smtp_password'] ?? get_setting('smtp_password', 'Money2025@Demo#');
$from_name = $_POST['from_name'] ?? get_setting('email_from_name', SITE_NAME);
$from_email = $_POST['from_email'] ?? get_setting('email_from_address', $smtp_username);

// Prepare email content
$subject = 'Test Email from ' . SITE_NAME;
$message = '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Test Email</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #4f46e5;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
        }
        .content {
            padding: 20px;
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            border-top: none;
            border-radius: 0 0 5px 5px;
        }
        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 12px;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Test Email</h1>
    </div>
    <div class="content">
        <p>Hello,</p>
        <p>This is a test email from ' . SITE_NAME . '.</p>
        <p>If you received this email, it means your email settings are configured correctly.</p>
        <p>SMTP Settings:</p>
        <ul>
            <li>Host: ' . $smtp_host . '</li>
            <li>Port: ' . $smtp_port . '</li>
            <li>Security: ' . ($smtp_secure ?: 'None') . '</li>
            <li>Username: ' . $smtp_username . '</li>
        </ul>
        <p>Best regards,<br>' . SITE_NAME . ' Team</p>
    </div>
    <div class="footer">
        <p>&copy; ' . date('Y') . ' ' . SITE_NAME . '. All rights reserved.</p>
    </div>
</body>
</html>
';

// Set up email options
$options = [
    'from_email' => $from_email,
    'from_name' => $from_name,
    'smtp' => [
        'enabled' => $use_smtp === '1',
        'host' => $smtp_host,
        'port' => $smtp_port,
        'username' => $smtp_username,
        'password' => $smtp_password,
        'secure' => $smtp_secure
    ]
];

// Send the email
$result = send_mail_with_phpmailer($email, $subject, $message, $options);

// Return the result
header('Content-Type: application/json');
echo json_encode([
    'success' => $result,
    'message' => $result ? 'Test email sent successfully.' : 'Failed to send test email. Please check your SMTP settings.'
]);
exit;
