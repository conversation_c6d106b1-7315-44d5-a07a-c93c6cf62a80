/**
 * Notifications CSS
 * 
 * This file contains styles for notifications.
 */

/* Notification container */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 300px;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: flex-start;
    transform: translateX(calc(100% + 20px));
    transition: transform 0.3s ease;
    z-index: 9999;
    background-color: white;
}

.notification.show {
    transform: translateX(0);
}

/* Notification types */
.notification-success {
    border-left: 4px solid #10b981;
}

.notification-error {
    border-left: 4px solid #ef4444;
}

.notification-warning {
    border-left: 4px solid #f59e0b;
}

.notification-info {
    border-left: 4px solid #3b82f6;
}

/* Notification icon */
.notification-icon {
    margin-right: 15px;
    font-size: 20px;
}

.notification-success .notification-icon {
    color: #10b981;
}

.notification-error .notification-icon {
    color: #ef4444;
}

.notification-warning .notification-icon {
    color: #f59e0b;
}

.notification-info .notification-icon {
    color: #3b82f6;
}

/* Notification content */
.notification-content {
    flex: 1;
}

.notification-content p {
    margin: 0;
    color: #374151;
}

/* Notification close button */
.notification-close {
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    font-size: 16px;
    padding: 0;
    margin-left: 10px;
}

.notification-close:hover {
    color: #6b7280;
}

/* User notification dropdown */
.notification-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 350px;
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 100;
    display: none;
    max-height: 400px;
    overflow-y: auto;
}

.notification-dropdown.show {
    display: block;
}

.notification-dropdown-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #e5e7eb;
}

.notification-dropdown-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.notification-dropdown-header .mark-all-read {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    font-size: 14px;
    padding: 0;
}

.notification-dropdown-body {
    padding: 0;
}

.notification-item {
    padding: 15px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: flex-start;
    transition: background-color 0.2s ease;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item:hover {
    background-color: #f9fafb;
}

.notification-item.unread {
    background-color: #f3f4f6;
}

.notification-item-icon {
    margin-right: 15px;
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.notification-item-content {
    flex: 1;
}

.notification-item-title {
    margin: 0 0 5px;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
}

.notification-item-message {
    margin: 0 0 5px;
    font-size: 14px;
    color: #6b7280;
}

.notification-item-time {
    font-size: 12px;
    color: #9ca3af;
}

.notification-dropdown-footer {
    padding: 15px;
    text-align: center;
    border-top: 1px solid #e5e7eb;
}

.notification-dropdown-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 14px;
}

.notification-dropdown-footer a:hover {
    text-decoration: underline;
}

/* Notification badge */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #ef4444;
    color: white;
    font-size: 10px;
    font-weight: 600;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Notification button */
.notification-button {
    position: relative;
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 0;
    font-size: 16px;
}

/* Loading indicator */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #6b7280;
}

.loading::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #e5e7eb;
    border-top-color: var(--primary-color);
    border-radius: 50%;
    margin-left: 10px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}