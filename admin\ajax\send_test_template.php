<?php
/**
 * Send Test Email Template
 *
 * This file handles sending a test email using a specific email template.
 *
 * @package LendSwift
 */

// Define LENDSWIFT constant to prevent direct access to included files
if (!defined('LENDSWIFT')) {
    define('LENDSWIFT', true);
}

// Include initialization file
require_once '../../includes/init.php';

// Check if the admin is logged in
if (!is_admin_logged_in()) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access.']);
    exit;
}

// Check if it's an AJAX request
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) !== 'xmlhttprequest') {
    echo json_encode(['success' => false, 'message' => 'Invalid request.']);
    exit;
}

// Verify CSRF token
if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid CSRF token.']);
    exit;
}

// Get required parameters
$email = isset($_POST['email']) ? sanitize_input($_POST['email']) : '';
$template_id = isset($_POST['template_id']) ? (int)$_POST['template_id'] : 0;

// Validate email
if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
    echo json_encode(['success' => false, 'message' => 'Invalid email address.']);
    exit;
}

// Validate template ID
if ($template_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid template ID.']);
    exit;
}

// Get database connection
$db = getDbConnection();

// Get template
$stmt = $db->prepare("SELECT * FROM email_templates WHERE id = ?");
$stmt->bind_param("i", $template_id);
$stmt->execute();
$result = $stmt->get_result();

if (!$result || $result->num_rows === 0) {
    echo json_encode(['success' => false, 'message' => 'Email template not found.']);
    exit;
}

$template = $result->fetch_assoc();

// Get admin information
$admin_id = get_current_admin_id();
$admin = get_admin_by_id($admin_id);

// Prepare email content
$subject = $template['subject'];
$content = $template['content'];

// Replace placeholders with sample data
$site_name = get_setting('site_name', 'LendSwift');
$site_url = get_setting('site_url', BASE_URL);
$user_name = $admin['name'] ?? 'John Doe';
$user_email = $email;
$loan_amount = '$5,000.00';
$loan_term = '12 months';
$loan_interest = '5.99%';
$loan_status = 'Approved';
$payment_amount = '$450.00';
$payment_date = date('F j, Y', strtotime('+1 month'));
$reset_link = BASE_URL . '/reset-password.php?token=sample_token';

// Replace placeholders
$replacements = [
    '{site_name}' => $site_name,
    '{site_url}' => $site_url,
    '{user_name}' => $user_name,
    '{user_email}' => $user_email,
    '{loan_amount}' => $loan_amount,
    '{loan_term}' => $loan_term,
    '{loan_interest}' => $loan_interest,
    '{loan_status}' => $loan_status,
    '{payment_amount}' => $payment_amount,
    '{payment_date}' => $payment_date,
    '{reset_link}' => $reset_link
];

$subject = str_replace(array_keys($replacements), array_values($replacements), $subject);
$content = str_replace(array_keys($replacements), array_values($replacements), $content);

// Send email
$result = send_mail_with_phpmailer($email, $subject, $content, [
    'from_email' => get_setting('email_from_address', '<EMAIL>'),
    'from_name' => get_setting('email_from_name', $site_name),
]);

if ($result) {
    echo json_encode(['success' => true, 'message' => 'Test email sent successfully.']);
} else {
    echo json_encode(['success' => false, 'message' => 'Failed to send test email. Please check your email settings.']);
}
