/**
 * Main Styles
 *
 * This file contains the main styles for the LendSwift Loan Management System.
 *
 * @package LendSwift
 */

:root {
    --primary-color: #4f46e5;
    --primary-hover: #4338ca;
    --primary-light: #e0e7ff;
    --text-color: #1f2937;
    --text-muted: #6b7280;
    --border-color: #e5e7eb;
    --background-color: #f9fafb;
    --card-background: #ffffff;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;
}

/* Base Styles */
body {
    font-family: 'Inter', sans-serif;
    color: var(--text-color);
    line-height: 1.5;
    margin: 0;
    padding: 0;
    background-color: var(--background-color);
}

a {
    color: var(--primary-color);
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

h1, h2, h3, h4, h5, h6 {
    margin-top: 0;
    color: var(--text-color);
}

p {
    margin-top: 0;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Buttons */
.button {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.button:hover {
    text-decoration: none;
}

.button-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
}

.button-primary:hover {
    background-color: var(--primary-hover);
}

.button-secondary {
    background-color: white;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.button-secondary:hover {
    background-color: var(--primary-light);
}

.button-sm {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

.button-lg {
    font-size: 1rem;
    padding: 0.75rem 1.5rem;
}

.button-block {
    display: block;
    width: 100%;
    text-align: center;
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    font-size: 0.875rem;
    color: var(--text-color);
}

.form-group .input-wrapper {
    position: relative;
}

.form-group .input-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    box-sizing: border-box;
    color: var(--text-color);
    background-color: var(--card-background);
}

.form-group input[type="checkbox"],
.form-group input[type="radio"] {
    width: auto;
    margin-right: 0.5rem;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-group .checkbox-wrapper {
    display: flex;
    align-items: center;
}

.form-group .checkbox-label {
    margin-bottom: 0;
    margin-left: 0.5rem;
}

/* Alerts */
.alert {
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
}

.alert-danger {
    background-color: #fee2e2;
    color: #b91c1c;
    border: 1px solid #fecaca;
}

.alert-success {
    background-color: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.alert-warning {
    background-color: #fff7ed;
    color: #c2410c;
    border: 1px solid #fed7aa;
}

.alert-info {
    background-color: #eff6ff;
    color: #1e40af;
    border: 1px solid #bfdbfe;
}

/* Cards */
.card {
    background-color: var(--card-background);
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.card-header {
    margin-bottom: 1.5rem;
}

.card-header h2 {
    margin-bottom: 0;
    font-size: 1.25rem;
}

.card-body {
    margin-bottom: 1.5rem;
}

.card-footer {
    border-top: 1px solid var(--border-color);
    padding-top: 1.5rem;
    display: flex;
    justify-content: flex-end;
}

/* Tables */
.table-responsive {
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    font-weight: 600;
    color: var(--text-muted);
    font-size: 0.75rem;
    text-transform: uppercase;
}

.table td {
    font-size: 0.875rem;
}

.table tr:last-child td {
    border-bottom: none;
}

/* Badges */
.badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.badge-primary {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

.badge-success {
    background-color: #d1fae5;
    color: #065f46;
}

.badge-warning {
    background-color: #fff7ed;
    color: #c2410c;
}

.badge-danger {
    background-color: #fee2e2;
    color: #b91c1c;
}

.badge-info {
    background-color: #eff6ff;
    color: #1e40af;
}

/* Utilities */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-left {
    text-align: left;
}

.text-muted {
    color: var(--text-muted);
}

.text-primary {
    color: var(--primary-color);
}

.text-success {
    color: var(--success-color);
}

.text-warning {
    color: var(--warning-color);
}

.text-danger {
    color: var(--error-color);
}

.text-info {
    color: var(--info-color);
}

.mb-0 {
    margin-bottom: 0;
}

.mb-1 {
    margin-bottom: 0.25rem;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mb-3 {
    margin-bottom: 1rem;
}

.mb-4 {
    margin-bottom: 1.5rem;
}

.mb-5 {
    margin-bottom: 3rem;
}

.mt-0 {
    margin-top: 0;
}

.mt-1 {
    margin-top: 0.25rem;
}

.mt-2 {
    margin-top: 0.5rem;
}

.mt-3 {
    margin-top: 1rem;
}

.mt-4 {
    margin-top: 1.5rem;
}

.mt-5 {
    margin-top: 3rem;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }
}

@media (max-width: 576px) {
    .button-block-sm {
        display: block;
        width: 100%;
        text-align: center;
    }
}