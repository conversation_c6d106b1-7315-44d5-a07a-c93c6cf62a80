/**
 * Sidebar Fix CSS
 *
 * This file contains styles to fix the sidebar scrollbar issue across all pages.
 *
 * @package LendSwift
 */

/* Hide scrollbars but allow scrolling */
.dashboard-sidebar {
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* IE and Edge */
    scroll-behavior: smooth !important;
    overflow-x: hidden !important;
}

.dashboard-sidebar::-webkit-scrollbar {
    display: none !important; /* Chrome, Safari and Opera */
    width: 0 !important;
    height: 0 !important;
}

/* Ensure smooth scrolling for all browsers */
html {
    scroll-behavior: smooth !important;
}

/* Additional fixes for specific pages */
.sidebar-nav {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* IE and Edge */
}

.sidebar-nav::-webkit-scrollbar {
    display: none !important; /* Chrome, Safari and Opera */
    width: 0 !important;
    height: 0 !important;
}

/* Fix for sidebar-nav-items */
.sidebar-nav-items {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* IE and Edge */
}

.sidebar-nav-items::-webkit-scrollbar {
    display: none !important; /* Chrome, Safari and Opera */
    width: 0 !important;
    height: 0 !important;
}

/* Fix for all scrollable elements */
* {
    scrollbar-width: thin !important;
    scrollbar-color: transparent transparent !important;
}

*::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
}

*::-webkit-scrollbar-track {
    background: transparent !important;
}

*::-webkit-scrollbar-thumb {
    background-color: transparent !important;
    border: none !important;
}

/* Fix for body and html */
body, html {
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
}

body::-webkit-scrollbar, html::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
}
