<?php
/**
 * LendSwift Improved Installer
 * 
 * This script helps set up the LendSwift application by:
 * 1. Creating the database if it doesn't exist
 * 2. Importing the database structure and data with improved error handling
 * 3. Updating the configuration file with the new credentials
 */

// Start session
session_start();

// Define constants
define('INSTALLER_PATH', __DIR__);
define('ROOT_PATH', dirname(__DIR__));
define('CONFIG_PATH', ROOT_PATH . '/includes/core/config.php');
define('SQL_FILE', INSTALLER_PATH . '/database.sql');

// Default database settings
$db_settings = [
    'host' => 'localhost',
    'name' => 'loan',
    'user' => 'root',
    'pass' => '',
    'charset' => 'utf8mb4'
];

// Default site settings
$site_settings = [
    'name' => 'PHARAOH FINANCE',
    'tagline' => 'PRIVATE AND FAST LOANS',
    'url' => 'http://localhost',
];

// Process form submission
$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$error = '';
$success = '';
$import_status = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step === 1 && isset($_POST['db_settings'])) {
        // Validate database settings
        $db_settings = [
            'host' => trim($_POST['db_host']),
            'name' => trim($_POST['db_name']),
            'user' => trim($_POST['db_user']),
            'pass' => $_POST['db_pass'],
            'charset' => 'utf8mb4'
        ];
        
        // Test database connection
        try {
            $mysqli = new mysqli(
                $db_settings['host'],
                $db_settings['user'],
                $db_settings['pass']
            );
            
            if ($mysqli->connect_error) {
                throw new Exception("Database connection failed: " . $mysqli->connect_error);
            }
            
            // Create database if it doesn't exist
            $mysqli->query("CREATE DATABASE IF NOT EXISTS `{$db_settings['name']}` CHARACTER SET {$db_settings['charset']}");
            
            if ($mysqli->error) {
                throw new Exception("Failed to create database: " . $mysqli->error);
            }
            
            // Select the database
            $mysqli->select_db($db_settings['name']);
            
            // Import SQL file with improved error handling
            $sql_content = file_get_contents(SQL_FILE);
            if (!$sql_content) {
                throw new Exception("Could not read SQL file.");
            }
            
            // Process SQL file
            $import_status = importSQL($mysqli, $sql_content);
            
            // Update config file
            $config_content = file_get_contents(CONFIG_PATH);
            if (!$config_content) {
                throw new Exception("Could not read configuration file.");
            }
            
            // Replace database settings
            $config_content = preg_replace(
                "/define\('DB_HOST', '.*?'\);/",
                "define('DB_HOST', '{$db_settings['host']}');",
                $config_content
            );
            $config_content = preg_replace(
                "/define\('DB_USER', '.*?'\);/",
                "define('DB_USER', '{$db_settings['user']}');",
                $config_content
            );
            $config_content = preg_replace(
                "/define\('DB_PASS', '.*?'\);/",
                "define('DB_PASS', '{$db_settings['pass']}');",
                $config_content
            );
            $config_content = preg_replace(
                "/define\('DB_NAME', '.*?'\);/",
                "define('DB_NAME', '{$db_settings['name']}');",
                $config_content
            );
            
            // Replace development mode setting
            $config_content = preg_replace(
                "/define\('DEVELOPMENT_MODE', true\);/",
                "define('DEVELOPMENT_MODE', false);",
                $config_content
            );
            
            // Write updated config file
            if (file_put_contents(CONFIG_PATH, $config_content) === false) {
                throw new Exception("Could not write to configuration file.");
            }
            
            $success = "Installation completed successfully!";
            $step = 2;
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

/**
 * Import SQL with improved error handling
 * 
 * @param mysqli $mysqli Database connection
 * @param string $sql_content SQL content to import
 * @return array Import status information
 */
function importSQL($mysqli, $sql_content) {
    $status = [
        'success' => true,
        'tables_created' => 0,
        'data_imported' => 0,
        'errors' => []
    ];
    
    // Remove comments and split into statements
    $sql_content = removeComments($sql_content);
    $statements = splitSQLIntoStatements($sql_content);
    
    // Execute each statement
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (empty($statement)) continue;
        
        // Execute the statement
        $result = $mysqli->query($statement);
        
        if (!$result) {
            $status['errors'][] = [
                'query' => substr($statement, 0, 100) . '...',
                'error' => $mysqli->error
            ];
            // Continue with next statement instead of stopping
        } else {
            // Count tables created
            if (preg_match('/CREATE TABLE/i', $statement)) {
                $status['tables_created']++;
            }
            // Count data imported
            if (preg_match('/INSERT INTO/i', $statement)) {
                $status['data_imported']++;
            }
        }
    }
    
    $status['success'] = count($status['errors']) === 0;
    return $status;
}

/**
 * Remove SQL comments
 * 
 * @param string $sql SQL content
 * @return string SQL content without comments
 */
function removeComments($sql) {
    $sql = preg_replace('/--.*$/m', '', $sql);
    $sql = preg_replace('!/\*.*?\*/!s', '', $sql);
    return $sql;
}

/**
 * Split SQL into individual statements
 * 
 * @param string $sql SQL content
 * @return array Array of SQL statements
 */
function splitSQLIntoStatements($sql) {
    $statements = [];
    $current = '';
    $delimiter = ';';
    
    // Split by lines
    $lines = explode("\n", $sql);
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;
        
        // Check for DELIMITER command
        if (preg_match('/^DELIMITER\s+(.+)$/i', $line, $matches)) {
            // If we have a statement, add it
            if (!empty($current)) {
                $statements[] = $current;
                $current = '';
            }
            $delimiter = $matches[1];
            continue;
        }
        
        // Add line to current statement
        $current .= $line . "\n";
        
        // Check if statement is complete
        if (substr($line, -strlen($delimiter)) === $delimiter) {
            $statements[] = $current;
            $current = '';
        }
    }
    
    // Add the last statement if not empty
    if (!empty($current)) {
        $statements[] = $current;
    }
    
    return $statements;
}

// HTML header
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LendSwift Installer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        h1, h2, h3 {
            color: #333;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"],
        input[type="password"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .form-hint {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .btn {
            display: inline-block;
            background-color: #4F46E5;
            color: #fff;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
        }
        .btn:hover {
            background-color: #4338CA;
        }
        .error {
            color: #e74c3c;
            background-color: #fdf7f7;
            border: 1px solid #e74c3c;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .success {
            color: #2ecc71;
            background-color: #f7fdf7;
            border: 1px solid #2ecc71;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .warning {
            color: #f39c12;
            background-color: #fef9e7;
            border: 1px solid #f39c12;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .status-box {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .error-details {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>LendSwift Installer</h1>
        
        <?php if (!empty($error)): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if (!empty($success)): ?>
            <div class="success"><?php echo $success; ?></div>
        <?php endif; ?>
        
        <?php if ($step === 1): ?>
            <p>Welcome to the LendSwift installer. This wizard will help you set up your loan management system.</p>
            
            <h2>Database Configuration</h2>
            <p>Please enter your database connection details below:</p>
            
            <form method="POST" action="<?php echo $_SERVER['PHP_SELF']; ?>?step=1">
                <input type="hidden" name="db_settings" value="1">
                
                <div class="form-group">
                    <label for="db_host">Database Host</label>
                    <input type="text" id="db_host" name="db_host" value="<?php echo htmlspecialchars($db_settings['host']); ?>" required>
                    <div class="form-hint">Usually "localhost" or "127.0.0.1"</div>
                </div>
                
                <div class="form-group">
                    <label for="db_name">Database Name</label>
                    <input type="text" id="db_name" name="db_name" value="<?php echo htmlspecialchars($db_settings['name']); ?>" required>
                    <div class="form-hint">The database will be created if it doesn't exist</div>
                </div>
                
                <div class="form-group">
                    <label for="db_user">Database Username</label>
                    <input type="text" id="db_user" name="db_user" value="<?php echo htmlspecialchars($db_settings['user']); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="db_pass">Database Password</label>
                    <input type="password" id="db_pass" name="db_pass" value="<?php echo htmlspecialchars($db_settings['pass']); ?>">
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn">Install</button>
                </div>
            </form>
        <?php elseif ($step === 2): ?>
            <h2>Installation Complete</h2>
            
            <?php if (!empty($import_status) && !empty($import_status['errors'])): ?>
                <div class="warning">
                    <h3>Installation completed with warnings</h3>
                    <p>The database was created, but some SQL statements could not be executed. Your system may still work, but some features might be missing.</p>
                    
                    <div class="status-box">
                        <p><strong>Tables created:</strong> <?php echo $import_status['tables_created']; ?></p>
                        <p><strong>Data import operations:</strong> <?php echo $import_status['data_imported']; ?></p>
                        <p><strong>Errors:</strong> <?php echo count($import_status['errors']); ?></p>
                        
                        <?php if (!empty($import_status['errors'])): ?>
                            <div class="error-details">
                                <?php foreach ($import_status['errors'] as $error): ?>
                                    <p><strong>Query:</strong> <?php echo htmlspecialchars($error['query']); ?></p>
                                    <p><strong>Error:</strong> <?php echo htmlspecialchars($error['error']); ?></p>
                                    <hr>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <p>LendSwift has been successfully installed on your server!</p>
            <p>You can now:</p>
            <ul>
                <li><a href="../index.php">Go to the homepage</a></li>
                <li><a href="../admin/login.php">Log in to the admin panel</a></li>
            </ul>
            <p><strong>Important:</strong> For security reasons, please delete the "install" directory after confirming everything works correctly.</p>
        <?php endif; ?>
    </div>
</body>
</html>
