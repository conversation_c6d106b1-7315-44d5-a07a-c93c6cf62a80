/**
 * User Login CSS
 *
 * This file contains styles for the user login, logout, and password reset pages.
 *
 * @package LendSwift
 */

:root {
    --primary-color: #4f46e5;
    --primary-hover: #4338ca;
    --primary-light: #e0e7ff;
    --text-color: #1f2937;
    --text-muted: #6b7280;
    --border-color: #e5e7eb;
    --background-color: #f9fafb;
    --card-background: #ffffff;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;
}

body.user-auth-page {
    background-color: var(--background-color);
    font-family: 'Inter', sans-serif;
    color: var(--text-color);
    line-height: 1.5;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-auth-container {
    width: 100%;
    max-width: 420px;
    padding: 2.5rem;
    background-color: var(--card-background);
    border-radius: 0.75rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.user-auth-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(to right, var(--primary-color), var(--primary-hover));
}

.user-auth-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.user-auth-logo {
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: center;
}

.user-auth-logo img {
    height: 70px;
    width: auto;
}

.user-auth-header h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
    margin: 0;
}

.user-auth-header p {
    color: var(--text-muted);
    margin-top: 0.5rem;
    font-size: 0.875rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    font-size: 0.875rem;
    color: var(--text-color);
}

.form-group .input-wrapper {
    position: relative;
}

.form-group .input-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

.form-group input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    box-sizing: border-box;
    color: var(--text-color);
    background-color: var(--card-background);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-group button {
    width: 100%;
    padding: 0.75rem 1rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.form-group button:hover {
    background-color: var(--primary-hover);
}

.alert {
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
}

.alert-danger {
    background-color: #fee2e2;
    color: #b91c1c;
    border: 1px solid #fecaca;
}

.alert-success {
    background-color: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.alert-warning {
    background-color: #fff7ed;
    color: #c2410c;
    border: 1px solid #fed7aa;
}

.alert-info {
    background-color: #eff6ff;
    color: #1e40af;
    border: 1px solid #bfdbfe;
}

.auth-links {
    text-align: center;
    margin-top: 1.5rem;
    font-size: 0.875rem;
}

.auth-links a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.auth-links a:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

.back-to-site {
    text-align: center;
    margin-top: 2rem;
    font-size: 0.875rem;
}

.back-to-site a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.back-to-site a:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

/* Password strength meter */
.password-strength-meter {
    height: 0.5rem;
    background-color: var(--border-color);
    border-radius: 0.25rem;
    margin-top: 0.5rem;
    overflow: hidden;
}

.password-strength-meter-bar {
    height: 100%;
    border-radius: 0.25rem;
    transition: width 0.3s ease, background-color 0.3s ease;
}

.password-strength-text {
    font-size: 0.75rem;
    margin-top: 0.25rem;
    text-align: right;
}

.strength-weak .password-strength-meter-bar {
    width: 25%;
    background-color: var(--error-color);
}

.strength-fair .password-strength-meter-bar {
    width: 50%;
    background-color: var(--warning-color);
}

.strength-good .password-strength-meter-bar {
    width: 75%;
    background-color: var(--info-color);
}

.strength-strong .password-strength-meter-bar {
    width: 100%;
    background-color: var(--success-color);
}

/* Error messages */
.error-message {
    color: var(--error-color);
    font-size: 0.75rem;
    margin-top: 0.25rem;
    display: none;
}

.input-error {
    border-color: var(--error-color) !important;
}

.input-error:focus {
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

.input-error + .error-message {
    display: block;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .user-auth-container {
        max-width: 100%;
        border-radius: 0;
        padding: 2rem 1.5rem;
        box-shadow: none;
    }
}