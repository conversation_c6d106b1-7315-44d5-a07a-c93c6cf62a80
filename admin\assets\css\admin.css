/*
 * LendSwift - Modern Loan Management System
 * Admin Panel Stylesheet
 */

:root {
    --primary-color: #4f46e5;
    --primary-hover: #4338ca;
    --primary-light: #e0e7ff;
    --secondary-color: #6b7280;
    --secondary-hover: #4b5563;
    --secondary-light: #f3f4f6;
    --success-color: #10b981;
    --success-hover: #059669;
    --success-light: #d1fae5;
    --danger-color: #ef4444;
    --danger-hover: #dc2626;
    --danger-light: #fee2e2;
    --warning-color: #f59e0b;
    --warning-hover: #d97706;
    --warning-light: #fef3c7;
    --info-color: #3b82f6;
    --info-hover: #2563eb;
    --info-light: #dbeafe;
    --text-color: #1f2937;
    --text-muted: #6b7280;
    --border-color: #e5e7eb;
    --background-color: #f9fafb;
    --card-background: #ffffff;
}

/* Admin Layout */
.admin-body {
    background-color: var(--background-color);
    font-family: 'Inter', sans-serif;
}

.admin-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.admin-sidebar {
    width: 250px;
    background-color: var(--card-background);
    color: var(--text-color);
    border-right: 1px solid var(--border-color);
    transition: width 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.admin-logo {
    display: flex;
    align-items: center;
}

/* Logo size classes */
.admin-logo img.logo-small {
    height: 45px;
}

.admin-logo img.logo-medium {
    height: 60px;
}

.admin-logo img.logo-large {
    height: 75px;
}

/* Custom logo size using CSS variables */
.admin-logo img {
    height: calc(60px * var(--logo-size-factor, 1));
    width: auto;
    margin-right: 0.5rem;
}

.admin-logo span {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
    white-space: nowrap;
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    font-size: 1.25rem;
}

.admin-nav {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.admin-nav ul {
    list-style: none;
    padding: 0.5rem 0;
    margin: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.admin-nav li {
    margin: 0.25rem 0;
}

.admin-nav li a {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.25rem;
    color: var(--text-color);
    text-decoration: none;
    border-radius: 0.25rem;
    margin: 0 0.5rem;
    transition: all 0.2s ease;
}

.admin-nav li a:hover {
    background-color: var(--secondary-light);
    color: var(--primary-color);
}

.admin-nav li.active a {
    background-color: var(--primary-light);
    color: var(--primary-color);
    font-weight: 500;
}

.admin-nav li a i {
    width: 20px;
    margin-right: 0.75rem;
    text-align: center;
}

.nav-badge {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background-color: var(--danger-color);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.125rem 0.375rem;
    border-radius: 0.75rem;
    min-width: 1.25rem;
    text-align: center;
}

/* Sidebar Logout Styling */
.sidebar-logout {
    margin-top: auto !important;
    border-top: 1px solid var(--border-color);
    padding-top: 0.5rem !important;
    margin-top: 1rem !important;
}

.sidebar-logout .logout-link {
    color: var(--danger-color) !important;
    font-weight: 500;
    transition: all 0.2s ease;
}

.sidebar-logout .logout-link:hover {
    background-color: rgba(239, 68, 68, 0.1) !important;
    color: var(--danger-color) !important;
    transform: translateX(2px);
}

.sidebar-logout .logout-link i {
    color: var(--danger-color);
}

/* Dropdown in sidebar */
.admin-nav .dropdown-toggle {
    position: relative;
    justify-content: space-between;
}

.admin-nav .dropdown-toggle .dropdown-icon {
    margin-left: 0.5rem;
    margin-right: 0;
    transition: transform 0.2s ease;
}

.admin-nav li.active .dropdown-toggle .dropdown-icon,
.admin-nav li:hover .dropdown-toggle .dropdown-icon {
    transform: rotate(180deg);
}

.admin-nav .dropdown-menu {
    position: static;
    display: none;
    width: auto;
    box-shadow: none;
    margin: 0;
    padding-left: 2.5rem;
    border: none;
    background: transparent;
}

.admin-nav .dropdown-menu::before {
    display: none;
}

.admin-nav li:hover .dropdown-menu,
.admin-nav li.active .dropdown-menu {
    display: block;
}

.admin-nav .dropdown-menu li {
    margin: 0;
}

.admin-nav .dropdown-menu a {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    border: none;
}

/* Admin Content */
.admin-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Admin Header */
.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background-color: var(--card-background);
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.header-search {
    flex: 1;
    max-width: 500px;
}

.header-search form {
    display: flex;
    position: relative;
}

.header-search input {
    width: 100%;
    padding: 0.5rem 1rem;
    padding-right: 2.5rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background-color: var(--secondary-light);
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.header-search input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.header-search button {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 2.5rem;
    background: none;
    border: none;
    color: var(--secondary-color);
    cursor: pointer;
}

.header-actions {
    display: flex;
    align-items: center;
}

.header-action {
    position: relative;
    margin-left: 1.5rem;
    color: var(--text-color);
    cursor: pointer;
    transition: color 0.2s ease;
}

.header-action:hover {
    color: var(--primary-color);
}

.header-action i {
    font-size: 1.25rem;
}

.header-action .badge {
    position: absolute;
    top: -8px;
    right: -8px;
    display: flex;
    justify-content: center;
    align-items: center;
    min-width: 18px;
    height: 18px;
    padding: 0 4px;
    background-color: var(--danger-color);
    color: #fff;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 9px;
}

.user-profile {
    display: flex;
    align-items: center;
}

.user-profile img {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    margin-right: 0.75rem;
    border: 2px solid var(--primary-light);
}

.user-profile span {
    font-weight: 500;
    color: var(--text-color);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    width: 280px;
    background-color: var(--card-background);
    border-radius: 0.75rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    z-index: 100;
    display: none;
    margin-top: 0.5rem;
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.dropdown-menu::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 16px;
    width: 12px;
    height: 12px;
    background-color: var(--card-background);
    transform: rotate(45deg);
    border-left: 1px solid var(--border-color);
    border-top: 1px solid var(--border-color);
}

.header-action:hover .dropdown-menu {
    display: block;
}

.dropdown-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.25rem;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--secondary-light);
}

.dropdown-header h3 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
}

.dropdown-header a {
    font-size: 0.875rem;
    color: var(--primary-color);
    font-weight: 500;
}

.dropdown-header a:hover {
    text-decoration: underline;
}

.dropdown-content {
    max-height: 350px;
    overflow-y: auto;
}

.empty-dropdown {
    padding: 1rem;
    text-align: center;
    color: var(--text-muted);
    font-size: 0.875rem;
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    padding: 0.875rem 1.25rem;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.2s ease;
    border-bottom: 1px solid var(--border-color);
}

.dropdown-menu a:last-child {
    border-bottom: none;
}

.dropdown-menu a:hover {
    background-color: var(--secondary-light);
    color: var(--primary-color);
}

.dropdown-menu a i {
    width: 20px;
    margin-right: 0.75rem;
    text-align: center;
    color: var(--secondary-color);
}

.dropdown-menu a:hover i {
    color: var(--primary-color);
}

.notification-item {
    display: flex;
    align-items: flex-start;
    padding: 0.875rem 1.25rem;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.2s ease;
}

.notification-item:hover {
    background-color: var(--secondary-light);
}

.notification-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background-color: var(--primary-light);
    color: var(--primary-color);
    border-radius: 50%;
    margin-right: 0.875rem;
    flex-shrink: 0;
}

.notification-content p {
    margin: 0 0 0.25rem;
    font-size: 0.875rem;
    color: var(--text-color);
}

.notification-time {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* Admin Main Content */
.admin-main {
    flex: 1;
    padding: 1.5rem;
    background-color: var(--background-color);
}

/* Admin Footer */
.admin-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background-color: var(--card-background);
    border-top: 1px solid var(--border-color);
    font-size: 0.875rem;
    color: var(--text-muted);
}

/* Dashboard */
.dashboard-header {
    margin-bottom: 2rem;
}

.dashboard-header h1 {
    margin-bottom: 0.5rem;
    color: var(--text-color);
    font-weight: 600;
}

.dashboard-header p {
    color: var(--text-muted);
    font-size: 0.875rem;
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    background-color: var(--card-background);
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--border-color);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.stat-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 48px;
    height: 48px;
    background-color: var(--primary-light);
    color: var(--primary-color);
    border-radius: 0.75rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.stat-icon i {
    font-size: 1.5rem;
}

.stat-content h3 {
    margin: 0 0 0.25rem;
    font-size: 0.875rem;
    color: var(--text-muted);
    font-weight: 500;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
}

.dashboard-row {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.dashboard-row:last-child {
    margin-bottom: 0;
}

.dashboard-row:last-child .dashboard-card {
    grid-column: 1 / -1;
}

.dashboard-card {
    background-color: var(--card-background);
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    border: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--card-background);
}

.card-header h2 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-color);
}

.button-small {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    border-radius: 0.375rem;
}

.card-content {
    padding: 1.5rem;
}

.data-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.data-table th,
.data-table td {
    padding: 0.875rem 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.data-table th {
    font-weight: 600;
    color: var(--text-muted);
    font-size: 0.875rem;
    background-color: var(--secondary-light);
    position: sticky;
    top: 0;
    z-index: 1;
}

.data-table th:first-child {
    border-top-left-radius: 0.5rem;
}

.data-table th:last-child {
    border-top-right-radius: 0.5rem;
}

.data-table tbody tr {
    transition: background-color 0.2s ease;
}

.data-table tbody tr:hover {
    background-color: var(--secondary-light);
}

.data-table tbody tr:last-child td {
    border-bottom: none;
}

.data-table tbody tr:last-child td:first-child {
    border-bottom-left-radius: 0.5rem;
}

.data-table tbody tr:last-child td:last-child {
    border-bottom-right-radius: 0.5rem;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.625rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 0.375rem;
    line-height: 1.5;
}

.status-badge::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 0.375rem;
}

.status-pending-review {
    background-color: var(--warning-light);
    color: var(--warning-hover);
}

.status-pending-review::before {
    background-color: var(--warning-color);
}

.status-under-assessment {
    background-color: var(--info-light);
    color: var(--info-hover);
}

.status-under-assessment::before {
    background-color: var(--info-color);
}

.status-approved {
    background-color: var(--success-light);
    color: var(--success-hover);
}

.status-approved::before {
    background-color: var(--success-color);
}

.status-rejected {
    background-color: var(--danger-light);
    color: var(--danger-hover);
}

.status-rejected::before {
    background-color: var(--danger-color);
}

.status-disbursed {
    background-color: var(--primary-light);
    color: var(--primary-hover);
}

.status-disbursed::before {
    background-color: var(--primary-color);
}

.status-closed {
    background-color: var(--secondary-light);
    color: var(--secondary-hover);
}

.status-closed::before {
    background-color: var(--secondary-color);
}

.status-active {
    background-color: var(--success-light);
    color: var(--success-hover);
}

.status-active::before {
    background-color: var(--success-color);
}

.status-inactive {
    background-color: var(--danger-light);
    color: var(--danger-hover);
}

.status-inactive::before {
    background-color: var(--danger-color);
}

.status-suspended {
    background-color: var(--warning-light);
    color: var(--warning-hover);
}

.status-suspended::before {
    background-color: var(--warning-color);
}

.status-pending {
    background-color: var(--warning-light);
    color: var(--warning-hover);
}

.status-pending::before {
    background-color: var(--warning-color);
}

/* Support Ticket Styles */
.direct-chat-messages {
    overflow-y: auto;
    padding: 1rem;
    background-color: var(--background-color);
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.direct-chat-msg {
    margin-bottom: 1.5rem;
}

.direct-chat-msg:last-child {
    margin-bottom: 0;
}

.direct-chat-infos {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.direct-chat-name {
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--text-color);
}

.direct-chat-timestamp {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.direct-chat-img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-light);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    float: left;
    margin-right: 10px;
}

.direct-chat-msg.right .direct-chat-img {
    float: right;
    margin-right: 0;
    margin-left: 10px;
    background-color: var(--secondary-light);
    color: var(--secondary-color);
}

.direct-chat-text {
    border-radius: 0.5rem;
    position: relative;
    padding: 0.75rem 1rem;
    background-color: #fff;
    border: 1px solid var(--border-color);
    margin-left: 50px;
    margin-right: 0;
    white-space: pre-wrap;
}

.direct-chat-msg.right .direct-chat-text {
    margin-right: 50px;
    margin-left: 0;
    background-color: var(--primary-light);
    border-color: var(--primary-light);
}

.direct-chat-text::after,
.direct-chat-text::before {
    position: absolute;
    right: 100%;
    top: 15px;
    border: solid transparent;
    content: ' ';
    height: 0;
    width: 0;
    pointer-events: none;
}

.direct-chat-text::after {
    border-color: rgba(255, 255, 255, 0);
    border-right-color: #fff;
    border-width: 6px;
    margin-top: -6px;
}

.direct-chat-text::before {
    border-color: rgba(229, 231, 235, 0);
    border-right-color: var(--border-color);
    border-width: 7px;
    margin-top: -7px;
}

.direct-chat-msg.right .direct-chat-text::after,
.direct-chat-msg.right .direct-chat-text::before {
    right: auto;
    left: 100%;
}

.direct-chat-msg.right .direct-chat-text::after {
    border-right-color: transparent;
    border-left-color: var(--primary-light);
}

.direct-chat-msg.right .direct-chat-text::before {
    border-right-color: transparent;
    border-left-color: var(--primary-light);
}

/* Priority Badge Styles */
.priority-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: capitalize;
    margin-left: 0.5rem;
}

.priority-badge::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.priority-high {
    background-color: var(--danger-light);
    color: var(--danger-color);
}

.priority-high::before {
    background-color: var(--danger-color);
}

.priority-medium {
    background-color: var(--warning-light);
    color: var(--warning-color);
}

.priority-medium::before {
    background-color: var(--warning-color);
}

.priority-low {
    background-color: var(--info-light);
    color: var(--info-color);
}

.priority-low::before {
    background-color: var(--info-color);
}

/* Status Badge Styles */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: capitalize;
}

.status-badge::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-open {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

.status-open::before {
    background-color: var(--primary-color);
}

.status-in_progress {
    background-color: var(--warning-light);
    color: var(--warning-color);
}

.status-in_progress::before {
    background-color: var(--warning-color);
}

.status-closed {
    background-color: var(--success-light);
    color: var(--success-color);
}

.status-closed::before {
    background-color: var(--success-color);
}

.badge-new {
    background-color: var(--danger-color);
    color: white;
    padding: 0.15rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    margin-left: 0.5rem;
}

/* Ticket Info Styles */
.ticket-info {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    flex: 1;
}

.ticket-info-item {
    display: flex;
    align-items: baseline;
}

.ticket-info-label {
    font-weight: 600;
    color: var(--text-muted);
    width: 120px;
    flex-shrink: 0;
}

.ticket-info-value {
    flex: 1;
}

.ticket-actions {
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
}

.ticket-details-section {
    background-color: var(--background-color);
    border-radius: 0.5rem;
    padding: 1.25rem;
    margin-bottom: 1.5rem;
    border: 1px solid var(--border-color);
}

.ticket-header-top {
    margin-bottom: 0.75rem;
}

.ticket-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--heading-color);
    margin: 0;
}

.ticket-badges {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.admin-card {
    background-color: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
    overflow: hidden;
    width: 100%;
}

.admin-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--card-background);
}

.admin-card-title {
    flex: 1;
}

.admin-card-title h3 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-color);
}

.admin-card-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.admin-card-body {
    padding: 1.5rem;
}

.admin-card-section {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

/* Support Ticket Specific Styles */
.message-list {
    padding: 1.25rem;
    margin: 1.5rem 0;
    position: relative;
}

/* No scrollbar needed as the chat expands naturally */

/* Date separator for messages */
.message-date-separator {
    text-align: center;
    margin: 1.5rem 0;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.message-date-separator::before {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-color);
    z-index: 1;
}

.message-date-separator span {
    background-color: var(--background-color);
    padding: 0 1rem;
    font-size: 0.75rem;
    color: var(--text-muted);
    position: relative;
    z-index: 2;
    font-weight: 500;
    border-radius: 1rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--border-color);
    padding: 0.25rem 0.75rem;
}

/* Message Styles */
.message-item {
    display: flex;
    margin-bottom: 1.5rem;
    animation: fadeIn 0.3s ease-in-out;
    transition: all 0.3s ease;
    position: relative;
}

.message-item.admin {
    flex-direction: row-reverse;
}

/* Add a subtle separator between messages */
.message-item:not(:last-child)::after {
    content: '';
    position: absolute;
    bottom: -0.75rem;
    left: 50%;
    transform: translateX(-50%);
    width: 85%;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(0,0,0,0.05), transparent);
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-light);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    margin-right: 1rem;
    flex-shrink: 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.message-item.admin .message-avatar {
    margin-right: 0;
    margin-left: 1rem;
    background-color: #d1ecd1; /* Light green background to match message */
    color: #2c662d; /* Darker green for the icon */
}

.message-content {
    max-width: 70%;
}

.message-bubble {
    background-color: #fff;
    color: var(--text-color);
    padding: 1rem;
    border-radius: 1rem;
    border-top-left-radius: 0;
    position: relative;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--border-color);
}

.message-item.admin .message-bubble {
    background-color: #e6f7e6; /* Light green background for admin messages */
    border-color: #d1ecd1;
    border-top-left-radius: 1rem;
    border-top-right-radius: 0;
    color: #2c662d; /* Slightly darker green text for better readability */
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.75rem;
    color: var(--text-muted);
}

.message-sender {
    font-weight: 600;
    color: var(--heading-color);
    font-size: 0.875rem;
}

.message-time {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.message-text {
    white-space: pre-wrap;
    line-height: 1.5;
}

/* Admin Support Form */
.admin-form {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
    position: relative;
}

.admin-form::before {
    content: '';
    position: absolute;
    top: -1px;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(to right, var(--primary-light), var(--primary-color), var(--primary-light));
    border-radius: 3px;
}

.admin-form textarea {
    border-radius: 0.5rem;
    resize: none;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.admin-form textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.admin-form .form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
}

.admin-form .button-primary {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1.25rem;
    transition: all 0.3s ease;
}

.admin-form .button-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.char-counter {
    font-size: 0.75rem;
    color: var(--text-muted);
    text-align: right;
    margin-top: 0.25rem;
}

.char-counter.limit-near {
    color: var(--warning-color);
}

.char-counter.limit-reached {
    color: var(--danger-color);
}

.reply-tips {
    font-size: 0.875rem;
    color: var(--text-muted);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.reply-tips i {
    color: var(--warning-color);
}

/* Notification Styles */
.notification-list {
    display: flex;
    flex-direction: column;
}

.notification-item {
    display: flex;
    padding: 1rem;
    border-radius: 0.25rem;
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
    margin-bottom: 0.5rem;
}

.notification-item:hover {
    background-color: var(--hover-color);
}

.notification-item.unread {
    background-color: var(--primary-light);
}

.notification-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--background-color);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.notification-icon.notification-support {
    color: #0369a1;
}

.notification-icon.notification-application {
    color: #92400e;
}

.notification-icon.notification-document {
    color: #166534;
}

.notification-icon.notification-transaction {
    color: #7e22ce;
}

.notification-icon.notification-user {
    color: #475569;
}

.notification-content {
    flex: 1;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
}

.notification-title {
    font-weight: 500;
    color: var(--heading-color);
    margin: 0;
    font-size: 0.9375rem;
}

.notification-time {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.notification-message {
    color: var(--text-color);
    margin-bottom: 0.5rem;
    font-size: 0.8125rem;
    line-height: 1.4;
}

.notification-actions {
    display: flex;
    gap: 0.75rem;
    font-size: 0.75rem;
}

.notification-link, .notification-mark-read {
    color: var(--primary-color);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    opacity: 0.85;
}

.notification-link:hover, .notification-mark-read:hover {
    opacity: 1;
}

.empty-state {
    text-align: center;
    padding: 2rem;
}

.empty-state-icon {
    font-size: 3rem;
    color: var(--text-muted);
    margin-bottom: 1rem;
}

.empty-state h3 {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--heading-color);
}

.empty-state p {
    color: var(--text-muted);
    margin-bottom: 1.5rem;
}

/* Admin Support Table */
.admin-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 0;
}

.admin-table th,
.admin-table td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.admin-table th {
    font-weight: 600;
    color: var(--heading-color);
    background-color: var(--background-color);
    white-space: nowrap;
}

.admin-table tr:hover {
    background-color: var(--hover-color);
}

.admin-table .empty-table {
    text-align: center;
    padding: 2rem;
    color: var(--text-muted);
}

.admin-table-responsive {
    overflow-x: auto;
    width: 100%;
    -webkit-overflow-scrolling: touch;
}

/* Admin Search Form */
.admin-search-form {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.admin-search-form .form-group {
    margin-bottom: 0;
}

.admin-search-form select,
.admin-search-form input {
    height: 38px;
    padding: 0.375rem 0.75rem;
}

.search-group {
    position: relative;
    display: flex;
    align-items: center;
}

.search-group input {
    padding-right: 2.5rem;
    width: 250px;
}

.search-button {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    background: none;
    border: none;
    padding: 0 0.75rem;
    color: var(--text-muted);
    cursor: pointer;
}

.search-button:hover {
    color: var(--primary-color);
}

/* Debug Mode Indicator */
.debug-mode-indicator {
    background-color: #fff3cd;
    color: #856404;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid #ffeeba;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

/* Error Message Formatting */
.flash-message.error {
    white-space: pre-wrap;
    font-family: monospace;
    max-height: 300px;
    overflow-y: auto;
    padding: 1rem;
}

.quick-actions-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.quick-actions-list li {
    margin-bottom: 0.5rem;
}

.quick-actions-list li:last-child {
    margin-bottom: 0;
}

.quick-actions-list a {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.2s ease;
}

.quick-actions-list a:hover {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

.quick-actions-list i {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    color: var(--primary-color);
    font-size: 1rem;
}

.quick-actions-list span {
    font-weight: 500;
    font-size: 0.9375rem;
}

/* Bar Chart Styles */
.chart-container {
    margin-top: 1rem;
    padding: 0 1rem;
}

.bar-chart {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
}

.chart-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.chart-label {
    font-size: 0.875rem;
    color: var(--text-color);
    font-weight: 500;
    width: 120px;
    text-align: right;
}

.chart-bar-container {
    height: 28px;
    background-color: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    flex-grow: 1;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

.chart-bar {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 0.75rem;
    color: white;
    font-size: 0.8125rem;
    font-weight: 600;
    transition: width 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    min-width: 40px;
    border-radius: 0 4px 4px 0;
}

.chart-value {
    margin-left: auto;
}

/* Status-specific colors for chart bars */
.chart-bar.status-pending,
.chart-bar.status-pending-review {
    background-color: #f59e0b;
}

.chart-bar.status-in-review,
.chart-bar.status-under-review {
    background-color: #3b82f6;
}

.chart-bar.status-approved {
    background-color: #10b981;
}

.chart-bar.status-rejected {
    background-color: #ef4444;
}

.chart-bar.status-disbursed {
    background-color: #8b5cf6;
}

.chart-bar.status-completed {
    background-color: #059669;
}

.chart-bar.status-cancelled {
    background-color: #6b7280;
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .dashboard-row {
        grid-template-columns: 3fr 2fr;
    }
}

@media (max-width: 992px) {
    .dashboard-row {
        grid-template-columns: 1fr;
    }

    .dashboard-row:last-child .dashboard-card {
        grid-column: auto;
    }

    .chart-item {
        gap: 0.75rem;
    }

    .chart-label {
        width: 100px;
    }
}

@media (max-width: 768px) {
    .chart-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .chart-label {
        width: 100%;
        text-align: left;
    }
}

@media (max-width: 768px) {
    .admin-sidebar {
        width: 70px;
    }

    .admin-logo span {
        display: none;
    }

    .admin-nav li a span {
        display: none;
    }

    .sidebar-toggle {
        display: block;
    }

    .admin-sidebar.expanded {
        width: 250px;
    }

    .admin-sidebar.expanded .admin-logo span,
    .admin-sidebar.expanded .admin-nav li a span {
        display: inline;
    }
}
