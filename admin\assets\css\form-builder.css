/**
 * Form Builder Styles
 *
 * This file contains styles for the form builder and dynamic forms.
 */

/* Form Builder Styles */
.form-builder-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    width: 100%;
}

.form-list {
    width: 100%;
}

.form-nav {
    list-style: none;
    padding: 0;
    margin: 0;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
}

.form-nav li {
    border-right: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
    flex: 1;
    min-width: 200px;
}

.form-nav li:last-child {
    border-right: none;
}

.form-nav li a {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    color: var(--text-color);
    text-decoration: none;
}

.form-nav li.active a {
    background-color: var(--primary-light);
    color: var(--primary-color);
    font-weight: 600;
}

.form-nav li a:hover {
    background-color: var(--secondary-light);
}

.status-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 0.25rem;
    margin-left: 0.5rem;
}

.status-active {
    background-color: #d1fae5;
    color: #065f46;
}

.status-inactive {
    background-color: #f3f4f6;
    color: #6b7280;
}

.status-default {
    background-color: #dbeafe;
    color: #1e40af;
}

.form-editor {
    flex: 1;
    width: 100%;
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.form-actions {
    display: flex;
    gap: 0.5rem;
}

.form-description {
    margin-bottom: 1.5rem;
    color: var(--text-muted);
}

.field-hint {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-top: 0.25rem;
}

/* Dynamic Form Styles */
.checkbox-group,
.radio-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.checkbox-item,
.radio-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-item input[type="checkbox"],
.radio-item input[type="radio"] {
    margin: 0;
}

.checkbox-item label,
.radio-item label {
    margin: 0;
    font-weight: normal;
}

/* Form Field Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="number"],
.form-group input[type="date"],
.form-group input[type="password"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.25rem;
    font-size: 1rem;
}

.form-group input[type="text"]:focus,
.form-group input[type="email"]:focus,
.form-group input[type="number"]:focus,
.form-group input[type="date"]:focus,
.form-group input[type="password"]:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-group.checkbox {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-group.checkbox input[type="checkbox"] {
    margin: 0;
}

.form-group.checkbox label {
    margin: 0;
    font-weight: normal;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.modal-content {
    position: relative;
    margin: 5% auto;
    width: 600px;
    max-width: 90%;
    background-color: var(--card-background);
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-muted);
}

.modal-body {
    padding: 1rem;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .form-nav {
        flex-direction: column;
    }
    
    .form-nav li {
        border-right: none;
    }
    
    .form-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .form-actions {
        width: 100%;
    }
    
    .modal-content {
        width: 95%;
        margin: 5% auto;
    }
}