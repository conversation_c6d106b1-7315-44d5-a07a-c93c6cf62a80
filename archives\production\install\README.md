# LendSwift Installation Guide

This directory contains the installation files for the LendSwift Loan Management System.

## Installation Files

1. **index.php** - Redirects to the improved installer
2. **improved_installer.php** - Main installation script that creates the database, tables, and initial configuration
3. **fix_missing_tables.php** - Utility to fix missing tables if the main installer encounters issues
4. **database.sql** - SQL file containing the database structure and initial data

## Installation Instructions

### Standard Installation

1. Upload all files to your web server
2. Navigate to the `/install` directory in your web browser (e.g., `http://yourdomain.com/install`)
3. Follow the installation wizard to set up your database
4. After installation is complete, delete the `/install` directory for security reasons

### Troubleshooting

If you encounter issues during installation, try the following:

1. **Database Connection Issues**
   - Verify your database credentials
   - Make sure your database user has sufficient privileges
   - Check if your hosting provider requires a specific database host

2. **SQL Import Issues**
   - If some tables are missing, use the `fix_missing_tables.php` script
   - Navigate to `http://yourdomain.com/install/fix_missing_tables.php`
   - Update your database credentials if needed
   - Select the missing tables to create

3. **File Permission Issues**
   - Make sure the web server has write permissions to the `includes/core/config.php` file
   - Typically, permissions should be set to 644 or 755

## Default Admin Login

After installation, you can log in to the admin panel with the following credentials:

- Email: <EMAIL>
- Password: admin123

**Important:** Change the default admin password immediately after your first login.

## Support

If you need assistance with the installation, <NAME_EMAIL>
