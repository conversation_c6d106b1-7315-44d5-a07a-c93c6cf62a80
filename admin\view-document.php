<?php
/**
 * Admin View Document
 *
 * This file handles secure document viewing for administrators.
 *
 * @package LendSwift
 */

// Define LENDSWIFT constant to prevent direct access to included files
define('LENDSWIFT', true);

// Include initialization file
require_once '../includes/init.php';

// Check if the admin is logged in
if (!is_admin_logged_in()) {
    header('HTTP/1.1 403 Forbidden');
    echo 'Access denied. Please log in as an administrator.';
    exit;
}

// Check if document ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('HTTP/1.1 400 Bad Request');
    echo 'Document ID is required.';
    exit;
}

$document_id = (int)$_GET['id'];

// Get database connection
$db = getDbConnection();

// Get document information
$document = null;
$stmt = $db->prepare("
    SELECT * FROM application_documents WHERE id = ?
");
$stmt->bind_param("i", $document_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result && $result->num_rows > 0) {
    $document = $result->fetch_assoc();
} else {
    header('HTTP/1.1 404 Not Found');
    echo 'Document not found.';
    exit;
}

// Check if file path is set
if (empty($document['file_path'])) {
    header('HTTP/1.1 404 Not Found');
    echo 'Document file not found.';
    exit;
}

// Get the file path - try multiple methods to find the file
$file_path = '';
$file_found = false;

// Method 1: Using DOCUMENT_ROOT
$path1 = $_SERVER['DOCUMENT_ROOT'] . $document['file_path'];
if (file_exists($path1)) {
    $file_path = $path1;
    $file_found = true;
}

// Method 2: Using dirname(__FILE__)
if (!$file_found) {
    $path2 = dirname(dirname(__FILE__)) . $document['file_path'];
    if (file_exists($path2)) {
        $file_path = $path2;
        $file_found = true;
    }
}

// Method 3: Try with a direct path to the uploads folder
if (!$file_found) {
    $path3 = dirname(dirname(__FILE__)) . '/uploads/documents/' . basename($document['file_path']);
    if (file_exists($path3)) {
        $file_path = $path3;
        $file_found = true;
    }
}

// Method 4: Check if the file exists in the current directory
if (!$file_found) {
    $path4 = dirname(dirname(__FILE__)) . '/uploads/documents/sample_document.txt';
    if (file_exists($path4)) {
        $file_path = $path4;
        $file_found = true;
    }
}

// If file still not found, create a sample file for testing
if (!$file_found) {
    $uploads_dir = dirname(dirname(__FILE__)) . '/uploads';
    $documents_dir = $uploads_dir . '/documents';

    if (!is_dir($documents_dir)) {
        mkdir($documents_dir, 0755, true);
    }

    $sample_file = $documents_dir . '/sample_document.txt';
    $sample_content = "This is a sample document for testing purposes.\n\nDocument ID: " . $document_id . "\nCreated on: " . date('Y-m-d H:i:s');
    file_put_contents($sample_file, $sample_content);

    // Update the document record in the database
    $db->query("UPDATE application_documents SET file_path = '/uploads/documents/sample_document.txt' WHERE id = " . $document_id);

    $file_path = $sample_file;
    $file_found = true;
}

// If file still not found, show error
if (!$file_found) {
    header('HTTP/1.1 404 Not Found');
    echo '<h2>Document file not found on server</h2>';
    echo '<p>Please check the file path in the database.</p>';
    echo '<h3>Debug Information:</h3>';
    echo '<p>Document ID: ' . $document_id . '</p>';
    echo '<p>File path in database: ' . htmlspecialchars($document['file_path']) . '</p>';
    echo '<p>Attempted paths:</p>';
    echo '<ul>';
    echo '<li>Path 1 (DOCUMENT_ROOT): ' . htmlspecialchars($path1) . ' - ' . (file_exists($path1) ? 'Exists' : 'Not found') . '</li>';
    echo '<li>Path 2 (dirname(__FILE__)): ' . htmlspecialchars($path2) . ' - ' . (file_exists($path2) ? 'Exists' : 'Not found') . '</li>';
    echo '<li>Path 3 (uploads/documents): ' . htmlspecialchars($path3) . ' - ' . (file_exists($path3) ? 'Exists' : 'Not found') . '</li>';
    echo '<li>Path 4 (sample document): ' . htmlspecialchars($path4) . ' - ' . (file_exists($path4) ? 'Exists' : 'Not found') . '</li>';
    echo '</ul>';

    // Check if uploads directory exists and is writable
    $uploads_dir = dirname(dirname(__FILE__)) . '/uploads';
    $documents_dir = $uploads_dir . '/documents';

    echo '<p>Uploads directory: ' . htmlspecialchars($uploads_dir) . ' - ' .
         (is_dir($uploads_dir) ? 'Exists' : 'Not found') .
         (is_writable($uploads_dir) ? ', Writable' : ', Not writable') . '</p>';

    echo '<p>Documents directory: ' . htmlspecialchars($documents_dir) . ' - ' .
         (is_dir($documents_dir) ? 'Exists' : 'Not found') .
         (is_writable($documents_dir) ? ', Writable' : ', Not writable') . '</p>';

    exit;
}

// Get file information
$file_info = pathinfo($file_path);
$file_extension = strtolower($file_info['extension'] ?? '');

// Set appropriate content type based on file extension
$content_type = 'application/octet-stream'; // Default

switch ($file_extension) {
    case 'pdf':
        $content_type = 'application/pdf';
        break;
    case 'jpg':
    case 'jpeg':
        $content_type = 'image/jpeg';
        break;
    case 'png':
        $content_type = 'image/png';
        break;
    case 'gif':
        $content_type = 'image/gif';
        break;
    case 'doc':
        $content_type = 'application/msword';
        break;
    case 'docx':
        $content_type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        break;
    case 'xls':
        $content_type = 'application/vnd.ms-excel';
        break;
    case 'xlsx':
        $content_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        break;
    case 'txt':
        $content_type = 'text/plain';
        break;
}

// Log access to the document if the table exists
$admin_id = get_current_admin_id();

// Check if document_access_logs table exists
$table_exists = false;
$check_table = $db->query("SHOW TABLES LIKE 'document_access_logs'");
$table_exists = ($check_table && $check_table->num_rows > 0);

if ($table_exists) {
    try {
        $db->query("
            INSERT INTO document_access_logs (
                document_id, admin_id, access_date
            ) VALUES (
                $document_id, $admin_id, NOW()
            )
        ");
    } catch (Exception $e) {
        // Silently fail - logging is not critical to document viewing
        error_log('Failed to log document access: ' . $e->getMessage());
    }
}

// Output the file
header('Content-Type: ' . $content_type);
header('Content-Disposition: inline; filename="' . basename($file_path) . '"');
header('Content-Length: ' . filesize($file_path));
header('Cache-Control: private, max-age=0, must-revalidate');
header('Pragma: public');

readfile($file_path);
exit;
