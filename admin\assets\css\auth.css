/**
 * Authentication Pages Styles
 *
 * This file contains styles for the login, forgot password, and reset password pages.
 *
 * @package LendSwift
 */

:root {
    --primary-color: #4f46e5;
    --primary-hover: #4338ca;
    --primary-light: #e0e7ff;
    --text-color: #1f2937;
    --text-muted: #6b7280;
    --border-color: #e5e7eb;
    --background-color: #f9fafb;
    --card-background: #ffffff;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;
    --font-family: 'Inter', sans-serif;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--background-color);
    color: var(--text-color);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    line-height: 1.5;
}

.auth-container {
    display: flex;
    width: 100%;
    max-width: 1000px;
    background-color: var(--card-background);
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
}

.auth-left {
    flex: 1;
    background-color: var(--primary-color);
    color: white;
    padding: 3rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.auth-left-content {
    max-width: 400px;
}

.auth-logo {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
}

.auth-logo img {
    height: 40px;
    width: auto;
    margin-right: 1rem;
}

.auth-logo-text {
    font-size: 1.5rem;
    font-weight: 700;
}

.auth-tagline {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.auth-description {
    margin-bottom: 2rem;
    opacity: 0.8;
    line-height: 1.6;
}

.auth-features {
    list-style: none;
}

.auth-feature {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.auth-feature i {
    margin-right: 0.75rem;
    font-size: 1.25rem;
}

.auth-right {
    flex: 1;
    padding: 3rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.auth-form-container {
    max-width: 400px;
    width: 100%;
}

.auth-form-header {
    margin-bottom: 2rem;
    text-align: center;
}

.auth-form-header h1 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.auth-form-header p {
    color: var(--text-muted);
    font-size: 0.875rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    font-size: 0.875rem;
}

.form-group .input-wrapper {
    position: relative;
}

.form-group .input-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

.form-group input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0;
    font-size: 1rem;
}

.password-toggle:hover {
    color: var(--primary-color);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-group .checkbox-wrapper {
    display: flex;
    align-items: center;
}

.form-group .checkbox-label {
    margin-left: 0.5rem;
    font-size: 0.875rem;
}

.form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.form-actions a {
    color: var(--primary-color);
    font-size: 0.875rem;
    text-decoration: none;
}

.form-actions a:hover {
    text-decoration: underline;
}

.auth-button {
    width: 100%;
    padding: 0.75rem 1rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.auth-button:hover {
    background-color: var(--primary-hover);
}

.auth-button i {
    margin-right: 0.5rem;
}

.auth-footer {
    text-align: center;
    margin-top: 2rem;
}

.auth-footer a {
    color: var(--primary-color);
    text-decoration: none;
}

.auth-footer a:hover {
    text-decoration: underline;
}

.alert {
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
}

.alert-danger {
    background-color: #fee2e2;
    color: #b91c1c;
    border: 1px solid #fecaca;
}

.alert-success {
    background-color: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.alert-warning {
    background-color: #fff7ed;
    color: #c2410c;
    border: 1px solid #fed7aa;
}

.alert-info {
    background-color: #eff6ff;
    color: #1e40af;
    border: 1px solid #bfdbfe;
}

.alert ul {
    margin: 0;
    padding-left: 1.5rem;
}

.error-message {
    color: var(--error-color);
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.input-error {
    border-color: var(--error-color) !important;
}

.input-error:focus {
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

/* Password strength meter */
.password-strength-meter {
    height: 0.25rem;
    background-color: var(--border-color);
    border-radius: 0.125rem;
    margin-top: 0.5rem;
    overflow: hidden;
}

.password-strength-meter-bar {
    height: 100%;
    border-radius: 0.125rem;
    transition: width 0.3s ease, background-color 0.3s ease;
}

.password-strength-text {
    font-size: 0.75rem;
    margin-top: 0.25rem;
    text-align: right;
}

.strength-weak .password-strength-meter-bar {
    width: 25%;
    background-color: var(--error-color);
}

.strength-fair .password-strength-meter-bar {
    width: 50%;
    background-color: var(--warning-color);
}

.strength-good .password-strength-meter-bar {
    width: 75%;
    background-color: var(--info-color);
}

.strength-strong .password-strength-meter-bar {
    width: 100%;
    background-color: var(--success-color);
}

/* Responsive styles */
@media (max-width: 768px) {
    .auth-container {
        flex-direction: column;
        max-width: 500px;
    }

    .auth-left {
        padding: 2rem;
    }

    .auth-right {
        padding: 2rem;
    }

    .auth-left-content {
        max-width: 100%;
    }

    .auth-form-container {
        max-width: 100%;
    }
}