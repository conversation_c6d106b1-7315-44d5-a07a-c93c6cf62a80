/**
 * User Dashboard Styles
 *
 * This file contains styles for the user dashboard.
 *
 * @package LendSwift
 */

:root {
    --primary-color: #4f46e5;
    --primary-hover: #4338ca;
    --primary-light: #e0e7ff;
    --text-color: #1f2937;
    --text-muted: #6b7280;
    --border-color: #e5e7eb;
    --background-color: #f9fafb;
    --card-background: #ffffff;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;
    --sidebar-width: 280px;
    --header-height: 64px;
    --font-family: 'Inter', sans-serif;
}

/* Global Styles */
body {
    font-family: var(--font-family);
    margin: 0;
    padding: 0;
    color: var(--text-color);
    background-color: var(--background-color);
    line-height: 1.5;
}

/* Dashboard Layout */
.dashboard-layout {
    display: flex;
    min-height: 100vh;
    background-color: var(--background-color);
}

/* Sidebar */
.dashboard-sidebar {
    width: var(--sidebar-width);
    background-color: var(--card-background);
    border-right: 1px solid var(--border-color);
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    z-index: 100;
    transition: transform 0.3s ease;
    overflow-y: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    scroll-behavior: smooth;
}

.dashboard-sidebar::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
}

.sidebar-header {
    padding: 1.5rem;
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
}

.sidebar-logo {
    display: flex;
    align-items: center;
}

/* Logo size classes */
.sidebar-logo img.logo-small {
    height: 36px;
}

.sidebar-logo img.logo-medium {
    height: 48px;
}

.sidebar-logo img.logo-large {
    height: 60px;
}

/* Custom logo size using CSS variables */
.sidebar-logo img {
    height: calc(48px * var(--logo-size-factor, 1));
    width: auto;
    margin-right: 0.75rem;
}

.sidebar-logo-text {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-color);
}

.sidebar-nav {
    padding: 1.5rem 0;
}

.sidebar-nav-title {
    padding: 0 1.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    color: var(--text-muted);
}

.sidebar-nav-items {
    list-style: none;
    padding: 0;
    margin: 0 0 1.5rem 0;
}

.sidebar-nav-item {
    margin-bottom: 0.25rem;
}

.sidebar-nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: var(--text-color);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: background-color 0.2s ease;
    border-left: 3px solid transparent;
}

.sidebar-nav-link:hover {
    background-color: var(--background-color);
    text-decoration: none;
}

.sidebar-nav-link.active {
    background-color: var(--primary-light);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
}

.sidebar-nav-icon {
    margin-right: 0.75rem;
    font-size: 1.25rem;
    color: var(--text-muted);
    width: 1.5rem;
    text-align: center;
}

.sidebar-nav-link.active .sidebar-nav-icon {
    color: var(--primary-color);
}

.sidebar-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.sidebar-user {
    display: flex;
    align-items: center;
}

.sidebar-user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-light);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-right: 0.75rem;
}

.sidebar-user-info {
    flex: 1;
}

.sidebar-user-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.sidebar-user-email {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.sidebar-logout {
    margin-top: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color);
    font-size: 0.875rem;
    text-decoration: none;
    background-color: #f3f4f6;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    font-weight: 500;
}

.sidebar-logout:hover {
    background-color: #fee2e2;
    color: #b91c1c;
    text-decoration: none;
}

.sidebar-logout i {
    margin-right: 0.5rem;
}

/* Main Content */
.dashboard-main {
    flex: 1;
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.dashboard-header {
    height: var(--header-height);
    background-color: var(--card-background);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    padding: 0 1.5rem;
    position: sticky;
    top: 0;
    z-index: 99;
}

.header-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
}

.header-actions {
    margin-left: auto;
    display: flex;
    align-items: center;
}

.header-action {
    margin-left: 1rem;
    position: relative;
}

.header-action-button {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s ease;
}

.header-action-button:hover {
    background-color: var(--background-color);
    color: var(--text-color);
}

.header-action-badge {
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--error-color);
    color: white;
    font-size: 0.625rem;
    font-weight: 600;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-sidebar-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: 1.5rem;
    cursor: pointer;
    margin-right: 1rem;
}

/* Content */
.dashboard-content {
    flex: 1;
    padding: 1.5rem;
}

.content-header {
    margin-bottom: 1.5rem;
}

.content-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.content-subtitle {
    color: var(--text-muted);
    font-size: 0.875rem;
}

/* Cards */
.card {
    background-color: var(--card-background);
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.card-actions {
    display: flex;
    align-items: center;
}

.card-action {
    margin-left: 0.75rem;
}

/* Stats */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    background-color: var(--card-background);
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 0.5rem;
    background-color: var(--primary-light);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-right: 1rem;
}

.stat-content {
    flex: 1;
}

.stat-title {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: 0.25rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.stat-change {
    font-size: 0.75rem;
    display: flex;
    align-items: center;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--error-color);
}

.stat-change i {
    margin-right: 0.25rem;
}

/* Tables */
.table-responsive {
    overflow-x: auto;
    margin-bottom: 1.5rem;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem 1rem;
    text-align: left;
}

.table th {
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    color: var(--text-muted);
    background-color: var(--background-color);
    border-bottom: 1px solid var(--border-color);
}

.table td {
    font-size: 0.875rem;
    border-bottom: 1px solid var(--border-color);
}

.table tr:last-child td {
    border-bottom: none;
}

.table-action {
    color: var(--text-muted);
    font-size: 1rem;
    text-decoration: none;
    margin-right: 0.5rem;
}

.table-action:hover {
    color: var(--primary-color);
}

/* Status Badges */
.status-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-badge-success {
    background-color: #d1fae5;
    color: #065f46;
}

.status-badge-warning {
    background-color: #fff7ed;
    color: #c2410c;
}

.status-badge-danger {
    background-color: #fee2e2;
    color: #b91c1c;
}

.status-badge-info {
    background-color: #eff6ff;
    color: #1e40af;
}

.status-badge-primary {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 1.5rem;
}

.pagination-item {
    margin: 0 0.25rem;
}

.pagination-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: var(--text-color);
    text-decoration: none;
    transition: background-color 0.2s ease;
}

.pagination-link:hover {
    background-color: var(--background-color);
    text-decoration: none;
}

.pagination-link.active {
    background-color: var(--primary-color);
    color: white;
}

.pagination-link.disabled {
    color: var(--text-muted);
    cursor: not-allowed;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 3rem 1.5rem;
}

.empty-state-icon {
    font-size: 3rem;
    color: var(--border-color);
    margin-bottom: 1.5rem;
}

.empty-state-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.empty-state-description {
    color: var(--text-muted);
    margin-bottom: 1.5rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Responsive */
@media (max-width: 992px) {
    .dashboard-sidebar {
        transform: translateX(-100%);
    }

    .dashboard-sidebar.show {
        transform: translateX(0);
    }

    .dashboard-main {
        margin-left: 0;
    }

    .mobile-sidebar-toggle {
        display: block;
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-header {
        padding: 0 1rem;
    }

    .dashboard-content {
        padding: 1rem;
    }
}

/* Notifications */
.notifications-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.notification-item {
    display: flex;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item.unread {
    background-color: var(--primary-light);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--background-color);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.notification-content {
    flex: 1;
}

.notification-text {
    margin: 0 0 0.25rem 0;
    font-size: 0.875rem;
    color: var(--text-color);
}

.notification-time {
    margin: 0;
    font-size: 0.75rem;
    color: var(--text-muted);
}

.badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    margin-left: 0.5rem;
}

.badge-primary {
    background-color: var(--primary-color);
    color: white;
}

/* Form Styles */
.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    font-size: 0.875rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-actions {
    margin-top: 2rem;
}

/* Dashboard Footer */
.dashboard-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    margin-top: 2rem;
}

.dashboard-footer .footer-content {
    text-align: center;
}

.dashboard-footer p {
    margin: 0;
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* Chart Styles */
.chart-container {
    position: relative;
    height: 350px;
    width: 100%;
    margin: 0 auto;
}

.chart-tabs {
    display: flex;
    gap: 0.5rem;
}

.chart-tab {
    background: none;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-muted);
    cursor: pointer;
    transition: all 0.2s ease;
}

.chart-tab:hover {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

.chart-tab.active {
    background-color: var(--primary-color);
    color: white;
}

.chart-description {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: 1rem;
    text-align: center;
    font-style: italic;
}