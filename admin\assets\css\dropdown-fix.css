/**
 * Dropdown Fix CSS
 *
 * This file contains styles to fix dropdown display issues across all pages.
 *
 * @package LendSwift
 */

/* Fix for all select dropdowns */
select {
    appearance: menulist !important;
    -webkit-appearance: menulist !important;
    -moz-appearance: menulist !important;
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color, #e5e7eb);
    border-radius: 0.25rem;
    background-color: #fff;
    font-size: 1rem;
}

/* Fix for all option elements */
option {
    display: block !important;
    white-space: normal !important;
    padding: 0.5rem !important;
    line-height: 1.5 !important;
    margin: 0.25rem 0 !important;
    border-bottom: 1px solid #f0f0f0 !important;
}

/* Specific fix for Employment Status dropdown */
select[id*="employment_status"] {
    appearance: menulist !important;
    -webkit-appearance: menulist !important;
    -moz-appearance: menulist !important;
}

select[id*="employment_status"] option {
    display: block !important;
    white-space: normal !important;
    padding: 0.5rem !important;
    line-height: 1.5 !important;
}

/* Fix for form builder dropdown options */
.form-group select {
    appearance: menulist !important;
    -webkit-appearance: menulist !important;
    -moz-appearance: menulist !important;
}

.form-group select option {
    display: block !important;
    white-space: normal !important;
    padding: 0.5rem !important;
    line-height: 1.5 !important;
}
