<?php
/**
 * Loan App Database Tester
 *
 * This script helps set up the Loan App by:
 * 1. Testing the database connection
 * 2. Creating the database if it doesn't exist
 * 3. Updating the configuration file with the new credentials
 */

// Start session
session_start();

// Define constants
define('INSTALLER_PATH', __DIR__);
define('ROOT_PATH', dirname(__DIR__));
define('CONFIG_PATH', ROOT_PATH . '/includes/core/config.php');
define('SQL_FILE', INSTALLER_PATH . '/database.sql');

// Default database settings
$db_settings = [
    'host' => 'localhost',
    'name' => 'loan',
    'user' => 'root',
    'pass' => '',
    'charset' => 'utf8mb4'
];

// Default site settings
$site_settings = [
    'name' => 'PHARAOH FINANCE',
    'tagline' => 'PRIVATE AND FAST LOANS',
    'url' => 'http://localhost',
];

// Process form submission
$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step === 1 && isset($_POST['db_settings'])) {
        // Validate database settings
        $db_settings = [
            'host' => trim($_POST['db_host']),
            'name' => trim($_POST['db_name']),
            'user' => trim($_POST['db_user']),
            'pass' => $_POST['db_pass'],
            'charset' => 'utf8mb4'
        ];

        // Test database connection
        try {
            $mysqli = new mysqli(
                $db_settings['host'],
                $db_settings['user'],
                $db_settings['pass']
            );

            if ($mysqli->connect_error) {
                throw new Exception("Database connection failed: " . $mysqli->connect_error);
            }

            // Create database if it doesn't exist
            $mysqli->query("CREATE DATABASE IF NOT EXISTS `{$db_settings['name']}` CHARACTER SET {$db_settings['charset']}");

            if ($mysqli->error) {
                throw new Exception("Failed to create database: " . $mysqli->error);
            }

            // Select the database
            $mysqli->select_db($db_settings['name']);

            // Check if database already has tables
            $result = $mysqli->query("SHOW TABLES");
            $tables = [];
            if ($result) {
                while ($row = $result->fetch_row()) {
                    $tables[] = $row[0];
                }
            }

            // Store database status
            $db_status = [
                'connected' => true,
                'tables_found' => count($tables),
                'tables_list' => $tables
            ];

            // Update config file
            $config_content = file_get_contents(CONFIG_PATH);
            if (!$config_content) {
                throw new Exception("Could not read configuration file.");
            }

            // Replace database settings
            $config_content = preg_replace(
                "/define\('DB_HOST', '.*?'\);/",
                "define('DB_HOST', '{$db_settings['host']}');",
                $config_content
            );
            $config_content = preg_replace(
                "/define\('DB_USER', '.*?'\);/",
                "define('DB_USER', '{$db_settings['user']}');",
                $config_content
            );
            $config_content = preg_replace(
                "/define\('DB_PASS', '.*?'\);/",
                "define('DB_PASS', '{$db_settings['pass']}');",
                $config_content
            );
            $config_content = preg_replace(
                "/define\('DB_NAME', '.*?'\);/",
                "define('DB_NAME', '{$db_settings['name']}');",
                $config_content
            );

            // Replace development mode setting
            $config_content = preg_replace(
                "/define\('DEVELOPMENT_MODE', true\);/",
                "define('DEVELOPMENT_MODE', false);",
                $config_content
            );

            // Write updated config file
            if (file_put_contents(CONFIG_PATH, $config_content) === false) {
                throw new Exception("Could not write to configuration file.");
            }

            $success = "Database connection test successful! Configuration has been updated.";
            $step = 2;

        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// HTML header
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Loan App Database Tester</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"],
        input[type="password"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .form-hint {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .btn {
            display: inline-block;
            background-color: #4F46E5;
            color: #fff;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
        }
        .btn:hover {
            background-color: #4338CA;
        }
        .error {
            color: #e74c3c;
            background-color: #fdf7f7;
            border: 1px solid #e74c3c;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .success {
            color: #2ecc71;
            background-color: #f7fdf7;
            border: 1px solid #2ecc71;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .warning {
            color: #f39c12;
            background-color: #fef9e7;
            border: 1px solid #f39c12;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .status-box {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .error-details {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Loan App Database Tester</h1>

        <?php if (!empty($error)): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="success"><?php echo $success; ?></div>
        <?php endif; ?>

        <?php if ($step === 1): ?>
            <p>Welcome to the Loan App Database Tester. This tool will test your database connection and update the configuration file.</p>
            <div class="warning">
                <p><strong>Note:</strong> This tool only tests the database connection. If your database is already set up, it will verify the connection. If not, you'll need to import the database structure manually.</p>
            </div>

            <h2>Database Configuration</h2>
            <p>Please enter your database connection details below:</p>

            <form method="POST" action="<?php echo $_SERVER['PHP_SELF']; ?>?step=1">
                <input type="hidden" name="db_settings" value="1">

                <div class="form-group">
                    <label for="db_host">Database Host</label>
                    <input type="text" id="db_host" name="db_host" value="<?php echo htmlspecialchars($db_settings['host']); ?>" required>
                    <div class="form-hint">Usually "localhost" or "127.0.0.1"</div>
                </div>

                <div class="form-group">
                    <label for="db_name">Database Name</label>
                    <input type="text" id="db_name" name="db_name" value="<?php echo htmlspecialchars($db_settings['name']); ?>" required>
                    <div class="form-hint">The database will be created if it doesn't exist</div>
                </div>

                <div class="form-group">
                    <label for="db_user">Database Username</label>
                    <input type="text" id="db_user" name="db_user" value="<?php echo htmlspecialchars($db_settings['user']); ?>" required>
                </div>

                <div class="form-group">
                    <label for="db_pass">Database Password</label>
                    <input type="password" id="db_pass" name="db_pass" value="<?php echo htmlspecialchars($db_settings['pass']); ?>">
                </div>

                <div class="form-group">
                    <button type="submit" class="btn">Test Database Connection</button>
                </div>
            </form>
        <?php elseif ($step === 2): ?>
            <h2>Database Test Complete</h2>

            <div class="status-box">
                <h3>Database Connection Status</h3>
                <p><strong>Connection:</strong> <?php echo isset($db_status['connected']) && $db_status['connected'] ? 'Connected successfully' : 'Not connected'; ?></p>
                <p><strong>Database:</strong> <?php echo htmlspecialchars($db_settings['name']); ?></p>
                <p><strong>Tables Found:</strong> <?php echo isset($db_status['tables_found']) ? $db_status['tables_found'] : 0; ?></p>

                <?php if (isset($db_status['tables_found']) && $db_status['tables_found'] > 0): ?>
                    <div class="success">
                        <p>Your database already contains tables. The database structure appears to be set up correctly.</p>
                    </div>
                <?php else: ?>
                    <div class="warning">
                        <p>No tables were found in your database. You need to import the database structure manually.</p>

                        <h4>Option 1: Using phpMyAdmin</h4>
                        <ol>
                            <li>Log in to your phpMyAdmin panel</li>
                            <li>Select the database: <strong><?php echo htmlspecialchars($db_settings['name']); ?></strong></li>
                            <li>Click on the "Import" tab</li>
                            <li>Click "Choose File" and select the SQL file from: <code><?php echo htmlspecialchars(SQL_FILE); ?></code></li>
                            <li>Click "Go" to import the database structure</li>
                        </ol>

                        <h4>Option 2: Using MySQL Command Line</h4>
                        <p>Run the following command from your server:</p>
                        <div class="error-details">
                            <code>mysql -u <?php echo htmlspecialchars($db_settings['user']); ?> -p<?php echo htmlspecialchars($db_settings['pass']); ?> <?php echo htmlspecialchars($db_settings['name']); ?> < <?php echo htmlspecialchars(SQL_FILE); ?></code>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <p>Next steps:</p>
            <ul>
                <li><a href="../index.php">Go to the homepage</a></li>
                <li><a href="../admin/login.php">Log in to the admin panel</a></li>
            </ul>

            <div class="warning">
                <p><strong>Important:</strong> For security reasons, please delete the "install" directory after confirming everything works correctly.</p>
            </div>

            <h3>Default Admin Login</h3>
            <p>After setting up the database, you can log in with:</p>
            <ul>
                <li><strong>Email:</strong> <EMAIL></li>
                <li><strong>Password:</strong> admin123</li>
            </ul>
            <p>Remember to change the default password after your first login!</p>
        <?php endif; ?>
    </div>
</body>
</html>