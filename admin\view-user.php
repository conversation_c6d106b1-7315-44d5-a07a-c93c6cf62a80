<?php
/**
 * Admin View User
 *
 * This file contains the functionality to view user details.
 *
 * @package LendSwift
 */

// Define LENDSWIFT constant to prevent direct access to included files
define('LENDSWIFT', true);

// Include initialization file
require_once '../includes/init.php';

// Check if the admin is logged in
if (!is_admin_logged_in()) {
    set_flash_message('error', 'Please log in to access the admin dashboard.');
    redirect(BASE_URL . '/admin/login.php');
}

// Get admin information
$admin_id = get_current_admin_id();
$admin_name = $_SESSION['admin_name'] ?? 'Administrator';

// Get database connection
$db = getDbConnection();

// Check if user ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    set_flash_message('error', 'User ID is required.');
    redirect(BASE_URL . '/admin/users.php');
}

$user_id = (int)$_GET['id'];

// Get user information
$user = null;
$stmt = $db->prepare("
    SELECT u.*, c.code as currency_code, c.symbol as currency_symbol, c.name as currency_name
    FROM users u
    LEFT JOIN currencies c ON u.currency_id = c.id
    WHERE u.id = ?
");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result && $result->num_rows > 0) {
    $user = $result->fetch_assoc();

    // Generate a reference number based on user ID and creation date
    // Format: USR-{YEAR}{MONTH}-{ID}{RANDOM}
    $creation_date = new DateTime($user['created_at']);
    $year = $creation_date->format('y');
    $month = $creation_date->format('m');
    $random_suffix = substr(md5($user['id'] . $user['email']), 0, 4);
    $user['reference_number'] = "USR-{$year}{$month}-{$user['id']}{$random_suffix}";
} else {
    set_flash_message('error', 'User not found.');
    redirect(BASE_URL . '/admin/users.php');
}

// Get user status history
$status_history = [];
$stmt = $db->prepare("
    SELECT ush.*, a.name as admin_name
    FROM user_status_history ush
    LEFT JOIN admins a ON ush.changed_by = a.id
    WHERE ush.user_id = ?
    ORDER BY ush.created_at DESC
    LIMIT 10
");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $status_history[] = $row;
    }
}

// Get user's loan applications
$applications = [];
$stmt = $db->prepare("
    SELECT la.*, lp.name as product_name, ls.name as status_name
    FROM loan_applications la
    JOIN loan_products lp ON la.loan_product_id = lp.id
    JOIN loan_statuses ls ON la.status_id = ls.id
    WHERE la.user_id = ?
    ORDER BY la.submission_date DESC
");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $applications[] = $row;
    }
}

// Include admin header
include '../includes/admin_header.php';
?>

<div class="view-user">
    <div class="page-header">
        <h1>User Profile</h1>
        <div class="page-actions">
            <a href="<?php echo BASE_URL; ?>/admin/edit-user.php?id=<?php echo $user_id; ?>" class="button button-primary">
                <i class="fas fa-edit"></i> Edit User
            </a>
            <a href="<?php echo BASE_URL; ?>/admin/users.php" class="button button-secondary">
                <i class="fas fa-arrow-left"></i> Back to Users
            </a>
        </div>
    </div>

    <div class="user-profile-header">
        <div class="user-avatar">
            <div class="avatar-placeholder">
                <?php echo strtoupper(substr($user['name'], 0, 1)); ?>
            </div>
        </div>
        <div class="user-info">
            <h2 class="user-name"><?php echo ucwords(htmlspecialchars($user['name'])); ?></h2>
            <p class="user-email"><?php echo htmlspecialchars($user['email']); ?></p>
            <span class="status-badge status-<?php echo $user['status']; ?>">
                <?php echo ucfirst($user['status']); ?>
            </span>
        </div>
    </div>

    <div class="tabs-container">
        <div class="tabs-header">
            <button class="tab-button" data-tab="loan-applications">Loan Applications</button>
            <button class="tab-button" data-tab="user-details">User Details</button>
            <button class="tab-button" data-tab="status-history">Status History</button>
        </div>

        <div class="tab-content">
            <!-- User Details Tab -->
            <div class="tab-pane" id="user-details">
                <div class="user-details-section">
                    <h3 class="section-title"><i class="fas fa-user-circle"></i> User Information</h3>
                    <div class="detail-list">
                        <div class="detail-item">
                            <div class="detail-label">Reference Number</div>
                            <div class="detail-value"><strong><?php echo $user['reference_number']; ?></strong></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Full Name</div>
                            <div class="detail-value"><strong><?php echo ucwords(htmlspecialchars($user['name'])); ?></strong></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Email Address</div>
                            <div class="detail-value"><strong><?php echo htmlspecialchars($user['email']); ?></strong></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Phone Number</div>
                            <div class="detail-value"><strong><?php echo htmlspecialchars($user['phone'] ?? 'Not provided'); ?></strong></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Account Status</div>
                            <div class="detail-value">
                                <span class="status-badge status-<?php echo $user['status']; ?>">
                                    <?php echo ucfirst($user['status']); ?>
                                </span>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Preferred Currency</div>
                            <div class="detail-value">
                                <span class="currency-badge">
                                    <i class="fas fa-money-bill-wave"></i>
                                    <?php echo $user['currency_symbol']; ?> <?php echo htmlspecialchars($user['currency_code']); ?> - <?php echo htmlspecialchars($user['currency_name']); ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="user-details-section">
                    <h3 class="section-title"><i class="fas fa-map-marker-alt"></i> Address Information</h3>
                    <div class="detail-list">
                        <div class="detail-item">
                            <div class="detail-label">Address</div>
                            <div class="detail-value"><strong><?php echo htmlspecialchars($user['address'] ?? 'Not provided'); ?></strong></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">City</div>
                            <div class="detail-value"><strong><?php echo htmlspecialchars($user['city'] ?? 'Not provided'); ?></strong></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">State/Province</div>
                            <div class="detail-value"><strong><?php echo htmlspecialchars($user['state'] ?? 'Not provided'); ?></strong></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">ZIP/Postal Code</div>
                            <div class="detail-value"><strong><?php echo htmlspecialchars($user['zip'] ?? 'Not provided'); ?></strong></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Country</div>
                            <div class="detail-value"><strong><?php echo htmlspecialchars($user['country'] ?? 'Not provided'); ?></strong></div>
                        </div>
                    </div>
                </div>

                <div class="user-details-section">
                    <h3 class="section-title"><i class="fas fa-clock"></i> Account Information</h3>
                    <div class="detail-list">
                        <div class="detail-item">
                            <div class="detail-label">Registered On</div>
                            <div class="detail-value"><strong><?php echo date('F j, Y \a\t g:i A', strtotime($user['created_at'])); ?></strong></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Last Updated</div>
                            <div class="detail-value">
                                <strong><?php echo $user['updated_at'] ? date('F j, Y \a\t g:i A', strtotime($user['updated_at'])) : 'Never updated'; ?></strong>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Total Loan Applications</div>
                            <div class="detail-value">
                                <strong class="loan-count"><?php echo count($applications); ?></strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status History Tab -->
            <div class="tab-pane" id="status-history">
                <div class="status-history-section">
                    <h3>Account Status History</h3>

                    <?php if (empty($status_history)): ?>
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <i class="fas fa-history"></i>
                            </div>
                            <h4>No Status History</h4>
                            <p>No status changes have been recorded for this user.</p>
                        </div>
                    <?php else: ?>
                        <div class="status-timeline">
                            <?php foreach ($status_history as $history): ?>
                                <div class="timeline-item">
                                    <div class="timeline-content">
                                        <div class="timeline-header">
                                            <span class="status-badge status-<?php echo $history['status']; ?>">
                                                <?php echo ucfirst($history['status']); ?>
                                            </span>
                                            <span class="timeline-date">
                                                <?php echo date('F j, Y \a\t g:i A', strtotime($history['created_at'])); ?>
                                            </span>
                                        </div>
                                        <div class="timeline-body">
                                            <p>
                                                <strong>Changed by:</strong> <?php echo htmlspecialchars($history['admin_name']); ?>
                                            </p>
                                            <?php if (!empty($history['admin_note'])): ?>
                                                <div class="admin-note">
                                                    <strong>Note:</strong> <?php echo nl2br(htmlspecialchars($history['admin_note'])); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Loan Applications Tab -->
            <div class="tab-pane" id="loan-applications">
                <div class="loan-applications-section">
                    <h3>Loan Applications</h3>

                    <?php if (empty($applications)): ?>
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <h4>No Applications</h4>
                            <p>This user has not submitted any loan applications yet.</p>
                        </div>
                    <?php else: ?>
                            <div class="table-responsive">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Product</th>
                                            <th>Amount</th>
                                            <th>Submission Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($applications as $application): ?>
                                            <tr>
                                                <td><?php echo $application['id']; ?></td>
                                                <td><?php echo htmlspecialchars($application['product_name']); ?></td>
                                                <td><?php echo $user['currency_symbol'] . number_format($application['applied_amount'], 2); ?></td>
                                                <td><?php echo date('M d, Y', strtotime($application['submission_date'])); ?></td>
                                                <td>
                                                    <span class="status-badge status-<?php echo strtolower(str_replace(' ', '-', $application['status_name'])); ?>">
                                                        <?php echo htmlspecialchars($application['status_name']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <a href="<?php echo BASE_URL; ?>/admin/application-details.php?id=<?php echo $application['id']; ?>" class="action-btn view-btn" title="View Details">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                                <path d="M10.5 8a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0z"/>
                                                                <path d="M0 8s3-5.5 8-5.5S16 8 16 8s-3 5.5-8 5.5S0 8 0 8zm8 3.5a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7z"/>
                                                            </svg>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .view-user {
        margin-bottom: 2rem;
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }

    .page-actions {
        display: flex;
        gap: 0.75rem;
    }

    /* User Profile Header */
    .user-profile-header {
        display: flex;
        align-items: center;
        margin-bottom: 30px;
        padding: 30px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .user-avatar {
        margin-right: 40px;
    }

    /* Tab System Styles */
    .tabs-container {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .tabs-header {
        display: flex;
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
    }

    .tab-button {
        padding: 15px 20px;
        background: none;
        border: none;
        font-size: 1rem;
        font-weight: 500;
        color: #6b7280;
        cursor: pointer;
        position: relative;
        transition: color 0.3s;
    }

    .tab-button:hover {
        color: #4f46e5;
    }

    .tab-button.active {
        color: #4f46e5;
        font-weight: 600;
    }

    .tab-button.active::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 3px;
        background-color: #4f46e5;
    }

    .tab-content {
        padding: 20px;
    }

    .tab-pane {
        display: none;
    }

    .tab-pane.active {
        display: block;
    }

    .card {
        background-color: #fff;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        margin-bottom: 1.5rem;
    }

    .card-header {
        padding: 1.25rem 1.5rem;
        border-bottom: 1px solid #e5e7eb;
    }

    .card-header h3 {
        margin: 0;
        font-size: 1.125rem;
        font-weight: 600;
    }

    .card-content {
        padding: 1.5rem;
    }

    .user-profile {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .user-avatar {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        margin-bottom: 1rem;
        overflow: hidden;
    }

    .avatar-placeholder {
        width: 100%;
        height: 100%;
        background-color: #4f46e5;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5rem;
        font-weight: 600;
    }

    .user-name {
        margin: 0 0 0.5rem;
        font-size: 1.25rem;
    }

    .user-email {
        margin: 0 0 0.75rem;
        color: #6b7280;
    }

    .detail-list {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
        background-color: #f9fafb;
        border-radius: 8px;
        padding: 15px;
    }

    .detail-item {
        display: flex;
        border-bottom: 1px solid #e5e7eb;
        padding-bottom: 0.75rem;
        padding-top: 0.75rem;
    }

    .detail-item:last-child {
        border-bottom: none;
        padding-bottom: 0;
    }

    .detail-item:hover {
        background-color: #f3f4f6;
        border-radius: 4px;
    }

    .detail-label {
        width: 40%;
        font-weight: 600;
        color: #4b5563;
        padding-left: 10px;
    }

    .detail-value {
        width: 60%;
        color: #1f2937;
    }

    .currency-badge {
        display: inline-flex;
        align-items: center;
        background-color: #f0f9ff;
        color: #0369a1;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
    }

    .currency-badge i {
        margin-right: 6px;
        color: #0284c7;
    }

    .user-details-section {
        margin-bottom: 30px;
    }

    .user-details-section h3.section-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e5e7eb;
        color: #4f46e5;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .user-details-section h3.section-title i {
        font-size: 20px;
    }

    .loan-count {
        background-color: #4f46e5;
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 14px;
    }

    .status-badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        font-weight: 600;
        border-radius: 0.25rem;
        border-left: none;
    }

    .status-active {
        background-color: #d1fae5;
        color: #065f46;
    }

    .status-inactive {
        background-color: #fee2e2;
        color: #b91c1c;
    }

    .status-suspended {
        background-color: #fef3c7;
        color: #92400e;
    }

    .status-pending-review {
        background-color: #fef3c7;
        color: #92400e;
    }

    .status-under-assessment {
        background-color: #e0e7ff;
        color: #3730a3;
    }

    .status-approved {
        background-color: #d1fae5;
        color: #065f46;
    }

    .status-rejected {
        background-color: #fee2e2;
        color: #b91c1c;
    }

    .status-disbursed {
        background-color: #dbeafe;
        color: #1e40af;
    }

    .status-closed {
        background-color: #e5e7eb;
        color: #374151;
    }

    /* Section Styles */
    .status-history-section,
    .user-details-section,
    .loan-applications-section {
        padding: 20px;
    }

    .status-history-section h3,
    .user-details-section h3,
    .loan-applications-section h3 {
        margin: 0 0 20px;
        font-size: 1.25rem;
        font-weight: 600;
        color: #1f2937;
    }

    /* Status Timeline Styles */
    .status-timeline {
        position: relative;
        padding: 1rem 0;
        margin-left: 0;
        border-left: none;
    }

    /* Removed vertical timeline line */

    .timeline-item {
        position: relative;
        margin-bottom: 1.5rem;
        padding-left: 0;
        border-left: none;
    }

    .timeline-item:last-child {
        margin-bottom: 0;
    }

    .timeline-badge {
        position: absolute;
        top: 0;
        left: -30px;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #fff;
        border: 2px solid #e5e7eb;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1;
    }

    .timeline-badge.status-active {
        border-color: #10b981;
    }

    .timeline-badge.status-inactive {
        border-color: #ef4444;
    }

    .timeline-badge.status-suspended {
        border-color: #f59e0b;
    }

    .timeline-badge i {
        font-size: 12px;
    }

    .timeline-badge.status-active i {
        color: #10b981;
    }

    .timeline-badge.status-inactive i {
        color: #ef4444;
    }

    .timeline-badge.status-suspended i {
        color: #f59e0b;
    }

    .timeline-content {
        background-color: #f9fafb;
        border-radius: 0.5rem;
        padding: 1rem;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        margin-left: 0;
        border-left: none;
    }

    .timeline-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;
    }

    .timeline-date {
        font-size: 0.875rem;
        color: #6b7280;
    }

    .timeline-body p {
        margin: 0 0 0.5rem;
    }

    .admin-note {
        background-color: #fff;
        padding: 0.75rem;
        margin-top: 0.75rem;
        border-radius: 0.25rem;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        border: none !important;
        border-left: none !important;
    }

    .empty-state {
        text-align: center;
        padding: 2rem 1rem;
    }

    .empty-state-icon {
        font-size: 2.5rem;
        color: #d1d5db;
        margin-bottom: 1rem;
    }

    .empty-state h4 {
        margin: 0 0 0.5rem;
    }

    .empty-state p {
        margin: 0;
        color: #6b7280;
    }

    .table-responsive {
        overflow-x: auto;
    }

    .data-table {
        width: 100%;
        border-collapse: collapse;
    }

    .data-table th,
    .data-table td {
        padding: 0.75rem;
        text-align: left;
        border-bottom: 1px solid #e5e7eb;
    }

    .data-table th {
        font-weight: 600;
        color: #6b7280;
    }

    .action-buttons {
        display: flex;
        gap: 0.75rem;
    }

    .action-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 6px;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
        color: #fff;
    }

    .view-btn {
        background-color: #3b82f6;
    }

    .view-btn:hover {
        background-color: #2563eb;
    }

    .button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 1.5rem;
        font-size: 0.95rem;
        font-weight: 500;
        border-radius: 0.5rem;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .button i {
        margin-right: 0.5rem;
    }

    .button-primary {
        background-color: #4f46e5;
        color: #fff;
    }

    .button-primary:hover {
        background-color: #4338ca;
    }

    .button-secondary {
        background-color: #f3f4f6;
        color: #374151;
        border: 1px solid #d1d5db;
    }

    .button-secondary:hover {
        background-color: #e5e7eb;
    }

    /* Status Timeline Styles */
    .status-timeline {
        position: relative;
        padding: 1rem 0;
    }

    .status-timeline:before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: 15px;
        width: 2px;
        background-color: #e5e7eb;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 1.5rem;
        padding-left: 40px;
    }

    .timeline-item:last-child {
        margin-bottom: 0;
    }

    .timeline-badge {
        position: absolute;
        top: 0;
        left: 0;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #fff;
        border: 2px solid #e5e7eb;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1;
    }

    .timeline-badge.status-active {
        border-color: #10b981;
    }

    .timeline-badge.status-inactive {
        border-color: #ef4444;
    }

    .timeline-badge.status-suspended {
        border-color: #f59e0b;
    }

    .timeline-badge i {
        font-size: 12px;
    }

    .timeline-badge.status-active i {
        color: #10b981;
    }

    .timeline-badge.status-inactive i {
        color: #ef4444;
    }

    .timeline-badge.status-suspended i {
        color: #f59e0b;
    }

    .timeline-content {
        background-color: #f9fafb;
        border-radius: 0.5rem;
        padding: 1rem;
    }

    .timeline-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;
    }

    .timeline-date {
        font-size: 0.875rem;
        color: #6b7280;
    }

    .timeline-body p {
        margin: 0 0 0.5rem;
    }

    .admin-note {
        background-color: #fff;
        border-left: 3px solid #4f46e5;
        padding: 0.75rem;
        margin-top: 0.75rem;
        border-radius: 0.25rem;
    }
</style>

<?php
// Include admin footer
include '../includes/admin_footer.php';
?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get all tab buttons and tab panes
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabPanes = document.querySelectorAll('.tab-pane');

        // Set initial active tab to Loan Applications
        tabButtons.forEach(btn => btn.classList.remove('active'));
        tabPanes.forEach(pane => pane.classList.remove('active'));

        const loanAppButton = document.querySelector('[data-tab="loan-applications"]');
        const loanAppPane = document.getElementById('loan-applications');

        loanAppButton.classList.add('active');
        loanAppPane.classList.add('active');

        // Add click event listeners to tab buttons
        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Get the tab ID from the data-tab attribute
                const tabId = this.getAttribute('data-tab');

                // Remove active class from all tab buttons and panes
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabPanes.forEach(pane => pane.classList.remove('active'));

                // Add active class to the clicked button and corresponding pane
                this.classList.add('active');
                document.getElementById(tabId).classList.add('active');
            });
        });
    });
</script>
