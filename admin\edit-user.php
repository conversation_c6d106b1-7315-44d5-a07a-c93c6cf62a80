<?php
/**
 * Admin Edit User
 *
 * This file contains the functionality to edit an existing user.
 *
 * @package LendSwift
 */

// Define LENDSWIFT constant to prevent direct access to included files
define('LENDSWIFT', true);

// Include initialization file
require_once '../includes/init.php';

// Check if the admin is logged in
if (!is_admin_logged_in()) {
    set_flash_message('error', 'Please log in to access the admin dashboard.');
    redirect(BASE_URL . '/admin/login.php');
}

// Get admin information
$admin_id = get_current_admin_id();
$admin_name = $_SESSION['admin_name'] ?? 'Administrator';

// Get database connection
$db = getDbConnection();

// Check if user ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    set_flash_message('error', 'User ID is required.');
    redirect(BASE_URL . '/admin/users.php');
}

$user_id = (int)$_GET['id'];

// Get user information
$user = null;
$stmt = $db->prepare("
    SELECT u.*, c.code as currency_code, c.symbol as currency_symbol
    FROM users u
    LEFT JOIN currencies c ON u.currency_id = c.id
    WHERE u.id = ?
");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result && $result->num_rows > 0) {
    $user = $result->fetch_assoc();
} else {
    set_flash_message('error', 'User not found.');
    redirect(BASE_URL . '/admin/users.php');
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!verify_csrf_token()) {
        set_flash_message('error', 'Invalid form submission. Please try again.');
        redirect(BASE_URL . '/admin/edit-user.php?id=' . $user_id);
    }

    // Get form data
    $name = ucwords(sanitize_input($_POST['name'] ?? ''));
    $email = sanitize_input($_POST['email'] ?? '');
    $phone = sanitize_input($_POST['phone'] ?? '');
    $address = sanitize_input($_POST['address'] ?? '');
    $city = sanitize_input($_POST['city'] ?? '');
    $state = sanitize_input($_POST['state'] ?? '');
    $zip = sanitize_input($_POST['zip'] ?? '');
    $country = sanitize_input($_POST['country'] ?? '');
    $password = $_POST['password'] ?? '';
    $currency_id = (int)($_POST['currency_id'] ?? 1);

    // Get status directly without sanitize_input to avoid escaping quotes
    $status = trim($_POST['status'] ?? 'active');

    // Convert to lowercase to ensure it matches the enum values
    $status = strtolower($status);

    // Validate against allowed values - these must match the database ENUM values exactly
    $allowed_statuses = ['active', 'inactive', 'suspended'];
    if (!in_array($status, $allowed_statuses)) {
        $status = 'active'; // Default to active if invalid status
        error_log("Invalid status value submitted: " . $_POST['status'] . ". Defaulting to 'active'.");
    }

    error_log("Status after validation: '$status'");

    // Validate form data
    $errors = [];

    if (empty($name)) {
        $errors[] = 'Name is required.';
    }

    if (empty($email)) {
        $errors[] = 'Email is required.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid email format.';
    } else {
        // Check if email already exists for other users
        $stmt = $db->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
        $stmt->bind_param("si", $email, $user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $errors[] = 'Email already exists.';
        }
    }

    // If password is provided, validate it
    if (!empty($password) && strlen($password) < 8) {
        $errors[] = 'Password must be at least 8 characters.';
    }

    // If no errors, update user
    if (empty($errors)) {
        try {
            // Log the start of the update process
            error_log("Starting user update process for user ID: $user_id");

            // Get current user status before updating
            $stmt = $db->prepare("SELECT status FROM users WHERE id = ?");
            if (!$stmt) {
                error_log("Prepare failed for status query: " . $db->error);
                throw new Exception("Database prepare error: " . $db->error);
            }

            $stmt->bind_param("i", $user_id);
            if (!$stmt->execute()) {
                error_log("Execute failed for status query: " . $stmt->error);
                throw new Exception("Database execute error: " . $stmt->error);
            }

            $result = $stmt->get_result();
            $current_status = '';

            if ($result && $result->num_rows > 0) {
                $current_status = $result->fetch_assoc()['status'];
                error_log("Current user status: $current_status, New status: $status");
            } else {
                error_log("No result found for user status query");
            }

            // Check if status is being changed
            $status_changed = ($current_status !== $status);

            // Prepare SQL based on whether password is being updated
            error_log("Preparing to update user data. Password update: " . (!empty($password) ? "Yes" : "No"));

            if (!empty($password)) {
                // Hash password
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                error_log("Password hashed successfully");

                // Update user with password
                $stmt = $db->prepare("
                    UPDATE users
                    SET name = ?, email = ?, phone = ?, address = ?, city = ?, state = ?, zip = ?, country = ?,
                        password = ?, currency_id = ?, status = ?, updated_at = NOW()
                    WHERE id = ?
                ");

                if (!$stmt) {
                    error_log("Prepare failed for user update with password: " . $db->error);
                    throw new Exception("Database prepare error: " . $db->error);
                }

                error_log("Binding parameters for update with password");
                if (!$stmt->bind_param("sssssssssiis", $name, $email, $phone, $address, $city, $state, $zip, $country,
                                 $hashed_password, $currency_id, $status, $user_id)) {
                    error_log("Bind param failed: " . $stmt->error);
                    throw new Exception("Parameter binding error: " . $stmt->error);
                }

                error_log("Parameters bound successfully for update with password");
            } else {
                // Update user without password
                error_log("Preparing update query without password");
                $stmt = $db->prepare("
                    UPDATE users
                    SET name = ?, email = ?, phone = ?, address = ?, city = ?, state = ?, zip = ?, country = ?,
                        currency_id = ?, status = ?, updated_at = NOW()
                    WHERE id = ?
                ");

                if (!$stmt) {
                    error_log("Prepare failed for user update without password: " . $db->error);
                    throw new Exception("Database prepare error: " . $db->error);
                }

                error_log("Binding parameters for update without password");
                if (!$stmt->bind_param("ssssssssiis", $name, $email, $phone, $address, $city, $state, $zip, $country,
                                 $currency_id, $status, $user_id)) {
                    error_log("Bind param failed: " . $stmt->error);
                    throw new Exception("Parameter binding error: " . $stmt->error);
                }

                error_log("Parameters bound successfully for update without password");
            }

            // If status is being changed, record it in history and send notification
            if ($status_changed) {
                error_log("Status is being changed from '$current_status' to '$status'");

                // Get current admin ID
                $admin_id = $_SESSION['admin_id'] ?? 1;
                error_log("Admin ID for status change: $admin_id");

                // Record status change in history
                error_log("Preparing to insert status history record");

                // Double-check status is valid for the history table
                // Convert to lowercase to ensure it matches the enum values exactly
                $status = strtolower($status);

                // Validate against allowed values - must match database ENUM values exactly
                $allowed_statuses = ['active', 'inactive', 'suspended'];
                if (!in_array($status, $allowed_statuses)) {
                    error_log("Invalid status for history table: $status. Defaulting to 'active'");
                    $status = 'active';
                }

                error_log("Status for history table after validation: '$status'");

                // Use direct SQL for status history insert to avoid enum issues
                $admin_note = "Status changed from {$current_status} to {$status} via user edit page.";
                $admin_note_escaped = $db->real_escape_string($admin_note);

                $history_sql = "INSERT INTO user_status_history (user_id, status, changed_by, admin_note)
                                VALUES ($user_id, '$status', $admin_id, '$admin_note_escaped')";

                error_log("Executing status history insert with SQL: $history_sql");

                if (!$db->query($history_sql)) {
                    error_log("Execute failed for status history: " . $db->error);
                    throw new Exception("Execute error for status history: " . $db->error);
                }

                error_log("Status history record inserted successfully");

                // Send email notification if the function exists
                $email_sent = false;
                if (function_exists('send_user_status_email')) {
                    error_log("Attempting to send status email notification");
                    $email_sent = send_user_status_email($user_id, $status, $admin_note);

                    if ($email_sent) {
                        error_log("Status email notification sent successfully");
                        $status_message = "User status updated and notification email sent.";
                    } else {
                        error_log("Failed to send status email notification");
                        $status_message = "User status updated but failed to send notification email.";
                    }
                } else {
                    error_log("send_user_status_email function not available");
                    $status_message = "User status updated. Email notification function not available.";
                }
            }

            // Final check for status value before executing the main update query
            error_log("Final status value check before update: '$status'");

            // Convert to lowercase to ensure it matches the enum values exactly
            $status = strtolower($status);

            // Validate against allowed values - must match database ENUM values exactly
            $allowed_statuses = ['active', 'inactive', 'suspended'];
            if (!in_array($status, $allowed_statuses)) {
                error_log("Invalid status detected before update execution: $status. Defaulting to 'active'");

                // Recreate the prepared statement with the correct status
                $status = 'active';

                if (!empty($password)) {
                    $stmt = $db->prepare("
                        UPDATE users
                        SET name = ?, email = ?, phone = ?, address = ?, city = ?, state = ?, zip = ?, country = ?,
                            password = ?, currency_id = ?, status = ?, updated_at = NOW()
                        WHERE id = ?
                    ");
                    $stmt->bind_param("sssssssssiis", $name, $email, $phone, $address, $city, $state, $zip, $country,
                                     $hashed_password, $currency_id, $status, $user_id);
                } else {
                    $stmt = $db->prepare("
                        UPDATE users
                        SET name = ?, email = ?, phone = ?, address = ?, city = ?, state = ?, zip = ?, country = ?,
                            currency_id = ?, status = ?, updated_at = NOW()
                        WHERE id = ?
                    ");
                    $stmt->bind_param("ssssssssiis", $name, $email, $phone, $address, $city, $state, $zip, $country,
                                     $currency_id, $status, $user_id);
                }
            }

            // Execute the main update query
            error_log("Executing main user update query with status: '$status'");

            // Use direct SQL for status update to avoid enum issues
            if (!empty($password)) {
                // Update user with password but without status
                $stmt = $db->prepare("
                    UPDATE users
                    SET name = ?, email = ?, phone = ?, address = ?, city = ?, state = ?, zip = ?, country = ?,
                        password = ?, currency_id = ?, updated_at = NOW()
                    WHERE id = ?
                ");

                if (!$stmt) {
                    error_log("Prepare failed for user update without status: " . $db->error);
                    throw new Exception("Database prepare error: " . $db->error);
                }

                if (!$stmt->bind_param("sssssssssii", $name, $email, $phone, $address, $city, $state, $zip, $country,
                                 $hashed_password, $currency_id, $user_id)) {
                    error_log("Bind param failed: " . $stmt->error);
                    throw new Exception("Parameter binding error: " . $stmt->error);
                }
            } else {
                // Update user without password and without status
                $stmt = $db->prepare("
                    UPDATE users
                    SET name = ?, email = ?, phone = ?, address = ?, city = ?, state = ?, zip = ?, country = ?,
                        currency_id = ?, updated_at = NOW()
                    WHERE id = ?
                ");

                if (!$stmt) {
                    error_log("Prepare failed for user update without status: " . $db->error);
                    throw new Exception("Database prepare error: " . $db->error);
                }

                if (!$stmt->bind_param("ssssssssii", $name, $email, $phone, $address, $city, $state, $zip, $country,
                                 $currency_id, $user_id)) {
                    error_log("Bind param failed: " . $stmt->error);
                    throw new Exception("Parameter binding error: " . $stmt->error);
                }
            }

            if (!$stmt->execute()) {
                error_log("Execute failed for user update: " . $stmt->error);
                throw new Exception("Database execute error: " . $stmt->error);
            }

            // Now update the status separately using direct SQL
            $status_sql = "UPDATE users SET status = '$status' WHERE id = $user_id";
            error_log("Executing status update with SQL: $status_sql");

            if (!$db->query($status_sql)) {
                error_log("Execute failed for status update: " . $db->error);
                throw new Exception("Database execute error for status update: " . $db->error);
            }

            error_log("User update executed successfully");

            if (isset($status_message)) {
                error_log("Setting success message with status: $status_message");
                set_flash_message('success', 'User updated successfully. ' . $status_message);
            } else {
                error_log("Setting generic success message");
                set_flash_message('success', 'User updated successfully.');
            }

            error_log("User update completed successfully, redirecting");
            redirect(BASE_URL . '/admin/users.php');
        } catch (Exception $e) {
            $error_message = $e->getMessage();
            $error_trace = $e->getTraceAsString();
            error_log('Edit User Error: ' . $error_message);
            error_log('Error Trace: ' . $error_trace);

            // Log detailed information about the error
            error_log('Error occurred while editing user ID: ' . $user_id);
            error_log('User data being updated: ' . json_encode([
                'name' => $name,
                'email' => $email,
                'phone' => $phone,
                'address' => $address,
                'city' => $city,
                'state' => $state,
                'zip' => $zip,
                'country' => $country,
                'currency_id' => $currency_id,
                'status' => $status,
                'password_changed' => !empty($password)
            ]));

            // In development environment, you might want to show the actual error
            // For production, use a generic message
            if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE === true) {
                set_flash_message('error', 'Error: ' . $error_message);
            } else {
                set_flash_message('error', 'An error occurred. Please try again later. Error has been logged.');
            }
        }
    } else {
        // Set error messages
        foreach ($errors as $error) {
            set_flash_message('error', $error);
        }
    }
}

// Get currencies
$currencies = [];
$result = $db->query("SELECT id, name, code, symbol FROM currencies ORDER BY name ASC");

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $currencies[] = $row;
    }
}

// Include admin header
include '../includes/admin_header.php';
?>

<div class="edit-user">
    <div class="page-header">
        <h1>Edit User</h1>
        <div class="page-actions">
            <a href="<?php echo BASE_URL; ?>/admin/users.php" class="button button-secondary">
                <i class="fas fa-arrow-left"></i> Back to Users
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2>User Information</h2>
        </div>
        <div class="card-content">
            <form method="POST" action="<?php echo BASE_URL; ?>/admin/edit-user.php?id=<?php echo $user_id; ?>">
                <?php echo csrf_token_field(); ?>

                <div class="form-section">
                    <h3 class="section-title"><i class="fas fa-user-circle"></i> Basic Information</h3>

                    <div class="form-group">
                        <label for="name">Full Name <span class="required">*</span></label>
                        <input type="text" id="name" name="name" class="form-control" value="<?php echo ucwords(htmlspecialchars($user['name'])); ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="email">Email Address <span class="required">*</span></label>
                        <input type="email" id="email" name="email" class="form-control" value="<?php echo htmlspecialchars($user['email']); ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="phone">Phone Number</label>
                        <input type="text" id="phone" name="phone" class="form-control" value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                    </div>
                </div>

                <div class="form-section">
                    <h3 class="section-title"><i class="fas fa-map-marker-alt"></i> Address Information</h3>

                    <div class="form-group">
                        <label for="address">Address</label>
                        <input type="text" id="address" name="address" class="form-control" value="<?php echo htmlspecialchars($user['address'] ?? ''); ?>">
                    </div>

                    <div class="form-row">
                        <div class="form-group half">
                            <label for="city">City</label>
                            <input type="text" id="city" name="city" class="form-control" value="<?php echo htmlspecialchars($user['city'] ?? ''); ?>">
                        </div>

                        <div class="form-group half">
                            <label for="state">State/Province</label>
                            <input type="text" id="state" name="state" class="form-control" value="<?php echo htmlspecialchars($user['state'] ?? ''); ?>">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group half">
                            <label for="zip">ZIP/Postal Code</label>
                            <input type="text" id="zip" name="zip" class="form-control" value="<?php echo htmlspecialchars($user['zip'] ?? ''); ?>">
                        </div>

                        <div class="form-group half">
                            <label for="country">Country</label>
                            <input type="text" id="country" name="country" class="form-control" value="<?php echo htmlspecialchars($user['country'] ?? ''); ?>">
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h3 class="section-title"><i class="fas fa-lock"></i> Security & Preferences</h3>

                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" class="form-control" autocomplete="new-password" placeholder="Leave blank to keep current password" value="">
                        <p class="field-hint">Leave blank to keep current password. New password must be at least 8 characters.</p>
                    </div>

                    <div class="form-group">
                        <label for="currency_id">Preferred Currency</label>
                        <div class="select-wrapper">
                            <select id="currency_id" name="currency_id" class="form-control styled-select">
                                <?php foreach ($currencies as $currency): ?>
                                    <option value="<?php echo $currency['id']; ?>" <?php echo ($currency['id'] == $user['currency_id']) ? 'selected' : ''; ?>>
                                        <?php echo $currency['symbol']; ?> <?php echo htmlspecialchars($currency['code']); ?> - <?php echo htmlspecialchars($currency['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="select-arrow">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                    <path d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z"/>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="status">Account Status</label>
                        <div class="select-wrapper">
                            <select id="status" name="status" class="form-control styled-select">
                                <option value="active" <?php echo strtolower($user['status']) === 'active' ? 'selected' : ''; ?>>Active</option>
                                <option value="inactive" <?php echo strtolower($user['status']) === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                <option value="suspended" <?php echo strtolower($user['status']) === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                            </select>
                            <div class="select-arrow">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                    <path d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="button button-primary">
                        <i class="fas fa-save"></i> Save Changes
                    </button>
                    <a href="<?php echo BASE_URL; ?>/admin/users.php" class="button button-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
    .edit-user {
        margin-bottom: 2rem;
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }

    .card {
        background-color: #fff;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .card-header {
        padding: 1.5rem;
        border-bottom: 1px solid #e5e7eb;
    }

    .card-header h2 {
        margin: 0;
        font-size: 1.25rem;
    }

    .card-content {
        padding: 1.5rem;
    }

    .form-section {
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid #e5e7eb;
    }

    .form-section:last-child {
        border-bottom: none;
        padding-bottom: 0;
    }

    .form-section .section-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 1.25rem;
        color: #4f46e5;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .form-section .section-title i {
        font-size: 1.2rem;
    }

    .form-row {
        display: flex;
        gap: 1.5rem;
        margin-bottom: 0;
    }

    .form-group {
        margin-bottom: 1.5rem;
        width: 100%;
    }

    .form-group.half {
        width: calc(50% - 0.75rem);
    }

    .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: #4b5563;
    }

    .form-control {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        font-size: 0.95rem;
    }

    .form-control:focus {
        outline: none;
        border-color: #4f46e5;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }

    .field-hint {
        margin-top: 0.5rem;
        font-size: 0.875rem;
        color: #6b7280;
    }

    .required {
        color: #ef4444;
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        margin-top: 2rem;
    }

    .button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 1.5rem;
        font-size: 0.95rem;
        font-weight: 500;
        border-radius: 0.5rem;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .button i {
        margin-right: 0.5rem;
    }

    .button-primary {
        background-color: #4f46e5;
        color: #fff;
    }

    .button-primary:hover {
        background-color: #4338ca;
    }

    .button-secondary {
        background-color: #f3f4f6;
        color: #374151;
        border: 1px solid #d1d5db;
    }

    .button-secondary:hover {
        background-color: #e5e7eb;
    }

    /* Styled Select */
    .select-wrapper {
        position: relative;
        width: 100%;
    }

    .styled-select {
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        width: 100%;
        padding: 0.75rem 1rem;
        font-size: 0.95rem;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        background-color: #fff;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .styled-select:focus {
        outline: none;
        border-color: #4f46e5;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }

    .select-arrow {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
        color: #6b7280;
    }
</style>

<?php
// Add JavaScript to clear password field on page load
echo '<script>
    // Clear password field on page load to prevent auto-fill
    document.addEventListener("DOMContentLoaded", function() {
        // Set timeout to ensure this runs after any browser auto-fill
        setTimeout(function() {
            var passwordField = document.getElementById("password");
            if (passwordField) {
                passwordField.value = "";
            }
        }, 100);
    });
</script>';

// Include admin footer
include '../includes/admin_footer.php';
?>
