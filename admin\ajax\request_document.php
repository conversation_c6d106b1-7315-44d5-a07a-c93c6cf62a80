<?php
/**
 * Request Document AJAX Handler
 *
 * This file handles document requests from admins to users.
 *
 * @package LendSwift
 */

// Define LENDSWIFT constant to prevent direct access to included files
define('LENDSWIFT', true);

// Include initialization file
require_once '../../includes/init.php';

// Check if the admin is logged in
if (!is_admin_logged_in()) {
    echo json_encode([
        'success' => false,
        'message' => 'You must be logged in as an admin to perform this action.'
    ]);
    exit;
}

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method.'
    ]);
    exit;
}

// Verify CSRF token
if (!verify_csrf_token()) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid CSRF token. Please refresh the page and try again.'
    ]);
    exit;
}

// Get request data
$user_id = isset($_POST['user_id']) ? (int)$_POST['user_id'] : 0;
$application_id = isset($_POST['application_id']) ? (int)$_POST['application_id'] : 0;
$document_type = $_POST['document_type'] ?? '';
$document_description = $_POST['document_description'] ?? '';
$admin_id = get_current_admin_id();

// Validate request data
if (empty($user_id) || empty($application_id) || empty($document_type)) {
    echo json_encode([
        'success' => false,
        'message' => 'Missing required fields.'
    ]);
    exit;
}

// Ensure document_type is not "0" or empty
if ($document_type === '0' || empty($document_type)) {
    echo json_encode([
        'success' => false,
        'message' => 'Please select a valid document type.'
    ]);
    exit;
}

// Get database connection
$db = getDbConnection();

// Check if the application exists and belongs to the user
$stmt = $db->prepare("
    SELECT * FROM loan_applications
    WHERE id = ? AND user_id = ?
");
$stmt->bind_param("ii", $application_id, $user_id);
$stmt->execute();
$result = $stmt->get_result();

if (!$result || $result->num_rows === 0) {
    echo json_encode([
        'success' => false,
        'message' => 'Application not found or does not belong to the specified user.'
    ]);
    exit;
}

// Get application details
$application = $result->fetch_assoc();

// Use the document type as the file_name
$file_name = $document_type;

// Log the document request for debugging
error_log("AJAX Document request - Type: $document_type, Name: $file_name");

// Create a new document request
$stmt = $db->prepare("
    INSERT INTO application_documents (
        application_id, user_id, file_name, document_type, document_description,
        requested_by_admin_id, status, upload_date
    ) VALUES (?, ?, ?, ?, ?, ?, 'pending', NOW())
");
$stmt->bind_param("iisssi", $application_id, $user_id, $file_name, $document_type, $document_description, $admin_id);

if (!$stmt->execute()) {
    echo json_encode([
        'success' => false,
        'message' => 'Failed to create document request. Please try again.'
    ]);
    exit;
}

$document_id = $db->insert_id;

// Verify the document was created correctly
$verify_stmt = $db->prepare("SELECT file_name, document_type FROM application_documents WHERE id = ?");
$verify_stmt->bind_param("i", $document_id);
$verify_stmt->execute();
$verify_result = $verify_stmt->get_result();

if ($verify_result && $verify_result->num_rows > 0) {
    $verify_row = $verify_result->fetch_assoc();
    error_log("AJAX Document verification - ID: $document_id, Name: {$verify_row['file_name']}, Type: {$verify_row['document_type']}");

    // If file_name is empty or "0", update it to match document_type
    if (empty($verify_row['file_name']) || $verify_row['file_name'] === '0') {
        $update_stmt = $db->prepare("UPDATE application_documents SET file_name = ? WHERE id = ?");
        $update_stmt->bind_param("si", $document_type, $document_id);
        $update_stmt->execute();
        error_log("AJAX Document name fixed - ID: $document_id, New Name: $document_type");
    }
}

// Create a notification for the user
$notification_title = 'Document Request';
$notification_message = "Admin has requested a document: $document_type. Please upload it as soon as possible.";
$notification_link = BASE_URL . "/?page=application-details&id=$application_id";

// Check if the notifications table has all required columns
$db_check = getDbConnection();
$columns_to_check = ['title', 'link', 'related_id', 'related_type'];
$missing_columns = [];

foreach ($columns_to_check as $column) {
    $result = $db_check->query("SHOW COLUMNS FROM notifications LIKE '$column'");
    if (!$result || $result->num_rows === 0) {
        $missing_columns[] = $column;
    }
}

// Add missing columns if needed
if (!empty($missing_columns)) {
    foreach ($missing_columns as $column) {
        try {
            if ($column === 'title') {
                $db_check->query("ALTER TABLE notifications ADD COLUMN title VARCHAR(255) NULL AFTER user_id");
            } elseif ($column === 'link') {
                $db_check->query("ALTER TABLE notifications ADD COLUMN link VARCHAR(255) NULL AFTER type");
            } elseif ($column === 'related_id') {
                $db_check->query("ALTER TABLE notifications ADD COLUMN related_id INT NULL AFTER link");
            } elseif ($column === 'related_type') {
                $db_check->query("ALTER TABLE notifications ADD COLUMN related_type VARCHAR(50) NULL AFTER related_id");
            }
            error_log("Added missing column $column to notifications table");
        } catch (Exception $e) {
            error_log("Error adding column $column to notifications table: " . $e->getMessage());
        }
    }
}

create_notification(
    $user_id,
    $notification_title,
    $notification_message,
    'warning',
    $notification_link,
    $document_id,
    'document'
);

// Get admin name
$admin_name = $_SESSION['admin_name'] ?? 'Admin';

// Send email notification
$user_email = '';
$user_name = '';

// Get user email and name
$stmt = $db->prepare("SELECT email, name FROM users WHERE id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result && $result->num_rows > 0) {
    $user = $result->fetch_assoc();
    $user_email = $user['email'];
    $user_name = $user['name'];
}

if (!empty($user_email)) {
    // Email subject
    $subject = "Document Request - " . SITE_NAME;

    // Email message
    $message = "
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { text-align: center; margin-bottom: 20px; }
                .logo { max-width: 150px; height: auto; }
                .content { background-color: #f9f9f9; padding: 20px; border-radius: 5px; }
                .footer { margin-top: 20px; font-size: 12px; color: #777; text-align: center; }
                .button { display: inline-block; background-color: #4f46e5; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <img src='" . BASE_URL . "/assets/images/logo.png' alt='" . SITE_NAME . "' class='logo'>
                </div>
                <div class='content'>
                    <h2>Document Request</h2>
                    <p>Hello " . htmlspecialchars($user_name) . ",</p>
                    <p>An admin has requested a document for your loan application #" . $application_id . ".</p>
                    <p><strong>Document Type:</strong> " . htmlspecialchars($document_type) . "</p>
                    <p><strong>Description:</strong> " . htmlspecialchars($document_description) . "</p>
                    <p>Please log in to your account and upload the requested document as soon as possible to continue with your loan application process.</p>
                    <p style='text-align: center;'>
                        <a href='" . $notification_link . "' class='button'>View Request</a>
                    </p>
                </div>
                <div class='footer'>
                    <p>This is an automated message from " . SITE_NAME . ". Please do not reply to this email.</p>
                </div>
            </div>
        </body>
        </html>
    ";

    // Send email
    send_email($user_email, $subject, $message);
}

// Return success response
echo json_encode([
    'success' => true,
    'message' => 'Document request created successfully.',
    'document_id' => $document_id
]);
exit;