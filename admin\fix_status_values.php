<?php
/**
 * Fix Status Values
 *
 * This file fixes any invalid status values in the database.
 *
 * @package LendSwift
 */

// Define LENDSWIFT constant to prevent direct access to included files
define('LENDSWIFT', true);

// Include initialization file
require_once '../includes/init.php';

// Check if the admin is logged in
if (!is_admin_logged_in()) {
    set_flash_message('error', 'Please log in to access the admin dashboard.');
    redirect(BASE_URL . '/admin/login.php');
}

// Get database connection
$db = getDbConnection();

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Fix Status Values</h1>";

// Check users table
echo "<h2>Users Table</h2>";
$result = $db->query("SELECT id, name, status FROM users");
echo "<table border='1'>";
echo "<tr><th>ID</th><th>Name</th><th>Current Status</th><th>Fixed Status</th><th>Action</th></tr>";

$fixed_count = 0;

while ($row = $result->fetch_assoc()) {
    $current_status = $row['status'];
    $fixed_status = strtolower($current_status);
    
    // Check if status needs fixing
    $needs_fixing = false;
    
    if (!in_array($fixed_status, ['active', 'inactive', 'suspended'])) {
        $fixed_status = 'active'; // Default to active if invalid
        $needs_fixing = true;
    } else if ($fixed_status !== $current_status) {
        // Status is valid but case is different
        $needs_fixing = true;
    }
    
    echo "<tr>";
    echo "<td>" . $row['id'] . "</td>";
    echo "<td>" . htmlspecialchars($row['name']) . "</td>";
    echo "<td>" . htmlspecialchars($current_status) . "</td>";
    echo "<td>" . htmlspecialchars($fixed_status) . "</td>";
    
    if ($needs_fixing) {
        // Fix the status
        $stmt = $db->prepare("UPDATE users SET status = ? WHERE id = ?");
        $stmt->bind_param("si", $fixed_status, $row['id']);
        $success = $stmt->execute();
        
        if ($success) {
            echo "<td style='color: green;'>Fixed</td>";
            $fixed_count++;
        } else {
            echo "<td style='color: red;'>Error: " . $db->error . "</td>";
        }
    } else {
        echo "<td>No action needed</td>";
    }
    
    echo "</tr>";
}
echo "</table>";
echo "<p>Fixed $fixed_count records in users table.</p>";

// Check user_status_history table
echo "<h2>User Status History Table</h2>";
$result = $db->query("SELECT id, user_id, status, created_at FROM user_status_history");
echo "<table border='1'>";
echo "<tr><th>ID</th><th>User ID</th><th>Current Status</th><th>Fixed Status</th><th>Created At</th><th>Action</th></tr>";

$fixed_count = 0;

while ($row = $result->fetch_assoc()) {
    $current_status = $row['status'];
    $fixed_status = strtolower($current_status);
    
    // Check if status needs fixing
    $needs_fixing = false;
    
    if (!in_array($fixed_status, ['active', 'inactive', 'suspended'])) {
        $fixed_status = 'active'; // Default to active if invalid
        $needs_fixing = true;
    } else if ($fixed_status !== $current_status) {
        // Status is valid but case is different
        $needs_fixing = true;
    }
    
    echo "<tr>";
    echo "<td>" . $row['id'] . "</td>";
    echo "<td>" . $row['user_id'] . "</td>";
    echo "<td>" . htmlspecialchars($current_status) . "</td>";
    echo "<td>" . htmlspecialchars($fixed_status) . "</td>";
    echo "<td>" . $row['created_at'] . "</td>";
    
    if ($needs_fixing) {
        // Fix the status
        $stmt = $db->prepare("UPDATE user_status_history SET status = ? WHERE id = ?");
        $stmt->bind_param("si", $fixed_status, $row['id']);
        $success = $stmt->execute();
        
        if ($success) {
            echo "<td style='color: green;'>Fixed</td>";
            $fixed_count++;
        } else {
            echo "<td style='color: red;'>Error: " . $db->error . "</td>";
        }
    } else {
        echo "<td>No action needed</td>";
    }
    
    echo "</tr>";
}
echo "</table>";
echo "<p>Fixed $fixed_count records in user_status_history table.</p>";

echo "<p><a href='" . BASE_URL . "/admin/users.php'>Return to Users</a></p>";
?>
