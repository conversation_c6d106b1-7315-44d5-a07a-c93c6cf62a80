<?php
/**
 * Admin Loan Applications Management
 *
 * This file contains the loan applications management functionality for administrators.
 *
 * @package LendSwift
 */

// Define LENDSWIFT constant to prevent direct access to included files
define('LENDSWIFT', true);

// Include initialization file
require_once '../includes/init.php';

// Check if the admin is logged in
if (!is_admin_logged_in()) {
    set_flash_message('error', 'Please log in to access the admin dashboard.');
    redirect(BASE_URL . '/admin/login.php');
}

// Get admin information
$admin_id = get_current_admin_id();
$admin_name = $_SESSION['admin_name'] ?? 'Administrator';

// Get database connection
$db = getDbConnection();

// Process application actions (update status)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!verify_csrf_token()) {
        set_flash_message('error', 'Invalid form submission. Please try again.');
        redirect(BASE_URL . '/admin/applications.php');
    }

    // Get action and application ID
    $action = sanitize_input($_POST['action'] ?? '');
    $application_id = sanitize_input($_POST['application_id'] ?? '');
    $status_id = sanitize_input($_POST['status_id'] ?? '');

    if ($action === 'update_status' && !empty($application_id) && !empty($status_id)) {
        try {
            // Update application status
            $db->query("UPDATE loan_applications SET status_id = $status_id WHERE id = $application_id");

            // If status is 'Approved' (assuming status_id 3), set release date to today
            if ($status_id == 3) {
                $today = date('Y-m-d');
                $db->query("UPDATE loan_applications SET release_date = '$today' WHERE id = $application_id");
            }

            set_flash_message('success', 'Application status updated successfully.');
        } catch (Exception $e) {
            error_log('Application Status Update Error: ' . $e->getMessage());
            set_flash_message('error', 'An error occurred. Please try again later.');
        }
    }

    // Handle application deletion
    if ($action === 'delete_application' && !empty($application_id)) {
        try {
            $result = deleteApplicationSafely($application_id, $admin_id);

            if ($result['success']) {
                set_flash_message('success', 'Application #' . $application_id . ' and all related data deleted successfully.');
            } else {
                set_flash_message('error', 'Deletion failed: ' . $result['error']);
            }
        } catch (Exception $e) {
            error_log('Application Deletion Error: ' . $e->getMessage());
            set_flash_message('error', 'An error occurred during deletion. Please try again later.');
        }
    }

    redirect(BASE_URL . '/admin/applications.php');
}

// Get applications with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 20; // Increased to 20 applications per page
$offset = ($page - 1) * $limit;

// Get filter parameters
$status_filter = isset($_GET['status']) ? (int)$_GET['status'] : 0;
$search = sanitize_input($_GET['search'] ?? '');

// Build query conditions
$conditions = [];
$params = [];

if ($status_filter > 0) {
    $conditions[] = "la.status_id = $status_filter";
}

if (!empty($search)) {
    $conditions[] = "(u.name LIKE '%$search%' OR u.email LIKE '%$search%' OR la.id LIKE '%$search%')";
}

$where_clause = !empty($conditions) ? "WHERE " . implode(" AND ", $conditions) : "";

// Get total applications count
$result = $db->query("
    SELECT COUNT(*) as count
    FROM loan_applications la
    JOIN users u ON la.user_id = u.id
    $where_clause
");
$row = $result->fetch_assoc();
$total_applications = $row['count'];
$total_pages = ceil($total_applications / $limit);

// Get applications for current page (including guest applications)
$applications = [];
$result = $db->query("
    SELECT la.*,
           CASE 
               WHEN la.is_guest_application = 1 THEN la.guest_name 
               ELSE u.name 
           END as user_name,
           CASE 
               WHEN la.is_guest_application = 1 THEN la.guest_email 
               ELSE u.email 
           END as user_email,
           CASE 
               WHEN la.is_guest_application = 1 THEN la.guest_phone 
               ELSE u.phone 
           END as user_phone,
           la.is_guest_application,
           lp.name as product_name, c.code as currency_code, c.symbol as currency_symbol,
           ls.name as status_name
    FROM loan_applications la
    LEFT JOIN users u ON la.user_id = u.id
    LEFT JOIN loan_products lp ON la.loan_product_id = lp.id
    LEFT JOIN currencies c ON la.currency_id = c.id
    LEFT JOIN loan_statuses ls ON la.status_id = ls.id
    $where_clause
    ORDER BY la.submission_date DESC
    LIMIT $offset, $limit
");

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $applications[] = $row;
    }
}

// Get all statuses for filter and status update
$statuses = [];
$result = $db->query("SELECT id, name FROM loan_statuses ORDER BY sort_order ASC");

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $statuses[] = $row;
    }
}

/**
 * Safely delete a loan application and all related data
 *
 * @param int $application_id The application ID to delete
 * @param int $admin_id The admin performing the deletion
 * @return array Result array with success status and message
 */
function deleteApplicationSafely($application_id, $admin_id) {
    $db = getDbConnection();

    try {
        $db->begin_transaction();

        // Get application details for logging
        $stmt = $db->prepare("SELECT * FROM loan_applications WHERE id = ?");
        $stmt->bind_param("i", $application_id);
        $stmt->execute();
        $application = $stmt->get_result()->fetch_assoc();

        if (!$application) {
            throw new Exception('Application not found');
        }

        // 1. Delete physical document files
        $docs_result = $db->query("SELECT file_path FROM application_documents WHERE application_id = $application_id");
        $deleted_files = [];

        if ($docs_result && $docs_result->num_rows > 0) {
            while ($doc = $docs_result->fetch_assoc()) {
                $file_path = BASE_PATH . '/' . ltrim($doc['file_path'], '/');
                if (file_exists($file_path)) {
                    if (unlink($file_path)) {
                        $deleted_files[] = $file_path;
                    }
                }
            }
        }

        // 2. Delete related records manually (in correct order to avoid FK violations)

        // Delete document access logs first (they reference application_documents)
        $db->query("DELETE FROM document_access_logs WHERE document_id IN (SELECT id FROM application_documents WHERE application_id = $application_id)");

        // Delete application documents
        $db->query("DELETE FROM application_documents WHERE application_id = $application_id");

        // Delete transactions related to this application
        $db->query("DELETE FROM transactions WHERE application_id = $application_id");

        // Delete notifications that mention this application
        $db->query("DELETE FROM notifications WHERE message LIKE '%Application #$application_id%' OR message LIKE '%application $application_id%'");

        // form_data will auto-delete due to existing CASCADE constraint

        // 3. Finally delete the application itself
        $db->query("DELETE FROM loan_applications WHERE id = $application_id");

        // 4. Log the deletion for audit trail
        $log_message = "Application #$application_id deleted by admin #$admin_id. ";
        $log_message .= "User: " . ($application['user_id'] ? "ID " . $application['user_id'] : "Guest " . $application['guest_email']);
        $log_message .= ", Amount: " . $application['applied_amount'];
        $log_message .= ", Files deleted: " . count($deleted_files);

        error_log($log_message);

        $db->commit();
        return ['success' => true, 'message' => 'Application deleted successfully', 'files_deleted' => count($deleted_files)];

    } catch (Exception $e) {
        $db->rollback();
        error_log('Application deletion error for ID ' . $application_id . ': ' . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// Include admin header
include '../includes/admin_header.php';
?>

<div class="applications-management">
    <div class="page-header">
        <h1>Loan Applications</h1>
        <div class="page-actions">
            <a href="application-reports.php" class="button button-secondary">
                <i class="fas fa-chart-bar"></i> Reports
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2>Applications</h2>
            <div class="card-actions">
                <form method="GET" action="applications.php" class="filter-form">
                    <div class="search-box">
                        <input type="text" name="search" placeholder="Search applications..." value="<?php echo htmlspecialchars($search); ?>">
                        <button type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <div class="filter-box">
                        <select name="status" onchange="this.form.submit()">
                            <option value="0">All Statuses</option>
                            <?php foreach ($statuses as $status): ?>
                                <option value="<?php echo $status['id']; ?>" <?php echo $status_filter == $status['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($status['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </form>
            </div>
        </div>

        <div class="card-content">
            <?php if (empty($applications)): ?>
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3>No Applications Found</h3>
                    <p>There are no loan applications matching your criteria.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Applicant</th>
                                <th>Product</th>
                                <th>Amount</th>
                                <th>Submission Date</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($applications as $application): ?>
                                <tr>
                                    <td><?php echo $application['id']; ?></td>
                                    <td>
                                        <div class="applicant-info">
                                            <div class="applicant-name">
                                                <?php echo htmlspecialchars($application['user_name']); ?>
                                                <?php if ($application['is_guest_application']): ?>
                                                    <span class="guest-badge">Guest</span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="applicant-email"><?php echo htmlspecialchars($application['user_email']); ?></div>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($application['product_name']); ?></td>
                                    <td><?php echo $application['currency_symbol'] . number_format($application['applied_amount'], 2); ?></td>
                                    <td><?php echo date('M d, Y', strtotime($application['submission_date'])); ?></td>
                                    <td>
                                        <span class="status-badge status-<?php echo strtolower(str_replace(' ', '-', $application['status_name'])); ?>">
                                            <?php echo htmlspecialchars($application['status_name']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="application-details.php?id=<?php echo $application['id']; ?>" class="action-btn view-btn" title="View Details">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                    <path d="M10.5 8a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0z"/>
                                                    <path d="M0 8s3-5.5 8-5.5S16 8 16 8s-3 5.5-8 5.5S0 8 0 8zm8 3.5a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7z"/>
                                                </svg>
                                            </a>

                                            <button type="button" class="action-btn edit-btn status-update-btn"
                                                    data-id="<?php echo $application['id']; ?>"
                                                    data-name="<?php echo htmlspecialchars($application['user_name']); ?>"
                                                    data-email="<?php echo htmlspecialchars($application['user_email']); ?>"
                                                    data-amount="<?php echo $application['currency_symbol'] . number_format($application['applied_amount'], 2); ?>"
                                                    data-product="<?php echo htmlspecialchars($application['product_name']); ?>"
                                                    data-status-id="<?php echo $application['status_id']; ?>"
                                                    data-status="<?php echo htmlspecialchars($application['status_name']); ?>"
                                                    title="Update Status">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                    <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
                                                </svg>
                                            </button>

                                            <button type="button" class="action-btn delete-btn delete-application-btn"
                                                    data-id="<?php echo $application['id']; ?>"
                                                    data-name="<?php echo htmlspecialchars($application['user_name']); ?>"
                                                    data-email="<?php echo htmlspecialchars($application['user_email']); ?>"
                                                    data-amount="<?php echo $application['currency_symbol'] . number_format($application['applied_amount'], 2); ?>"
                                                    data-product="<?php echo htmlspecialchars($application['product_name']); ?>"
                                                    title="Delete Application">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                    <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                                                    <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                                                </svg>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <?php if ($total_pages > 1): ?>
                    <div class="pagination">
                        <?php
                        // Build pagination URL with existing query parameters
                        $query_params = [];
                        if ($status_filter > 0) $query_params[] = "status=$status_filter";
                        if (!empty($search)) $query_params[] = "search=" . urlencode($search);
                        $query_string = !empty($query_params) ? "&" . implode("&", $query_params) : "";
                        ?>

                        <?php if ($page > 1): ?>
                            <a href="applications.php?page=<?php echo $page - 1 . $query_string; ?>" class="pagination-item">
                                <i class="fas fa-chevron-left"></i> Previous
                            </a>
                        <?php endif; ?>

                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <a href="applications.php?page=<?php echo $i . $query_string; ?>" class="pagination-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                            <a href="applications.php?page=<?php echo $page + 1 . $query_string; ?>" class="pagination-item">
                                Next <i class="fas fa-chevron-right"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
    .applications-management {
        margin-bottom: 2rem;
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }

    .card {
        background-color: #fff;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem;
        border-bottom: 1px solid #e5e7eb;
    }

    .card-header h2 {
        margin: 0;
        font-size: 1.25rem;
    }

    .card-actions {
        display: flex;
        gap: 1rem;
    }

    .filter-form {
        display: flex;
        gap: 1rem;
    }

    .search-box {
        position: relative;
    }

    .search-box input {
        padding: 0.5rem 2.5rem 0.5rem 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.25rem;
        min-width: 200px;
    }

    .search-box button {
        position: absolute;
        right: 0;
        top: 0;
        height: 100%;
        width: 2.5rem;
        background: none;
        border: none;
        color: #6b7280;
        cursor: pointer;
    }

    .filter-box select {
        padding: 0.5rem 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.25rem;
    }

    .card-content {
        padding: 1.5rem;
    }

    .empty-state {
        text-align: center;
        padding: 3rem 1.5rem;
    }

    .empty-state-icon {
        font-size: 3rem;
        color: #d1d5db;
        margin-bottom: 1rem;
    }

    .table-responsive {
        overflow-x: auto;
    }

    .data-table {
        width: 100%;
        border-collapse: collapse;
    }

    .data-table th,
    .data-table td {
        padding: 0.75rem;
        text-align: left;
        border-bottom: 1px solid #e5e7eb;
    }

    .data-table th {
        font-weight: 600;
        color: #6b7280;
    }

    .applicant-info {
        display: flex;
        flex-direction: column;
    }

    .applicant-email {
        font-size: 0.875rem;
        color: #6b7280;
    }

    .guest-badge {
        display: inline-block;
        padding: 0.125rem 0.375rem;
        font-size: 0.625rem;
        font-weight: 600;
        background-color: #fef3c7;
        color: #92400e;
        border-radius: 0.25rem;
        margin-left: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.025em;
    }

    .status-badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        font-weight: 600;
        border-radius: 0.25rem;
    }

    .status-pending-review {
        background-color: #fef3c7;
        color: #92400e;
    }

    .status-under-assessment {
        background-color: #e0e7ff;
        color: #3730a3;
    }

    .status-approved {
        background-color: #d1fae5;
        color: #065f46;
    }

    .status-rejected {
        background-color: #fee2e2;
        color: #b91c1c;
    }

    .status-disbursed {
        background-color: #dbeafe;
        color: #1e40af;
    }

    .status-closed {
        background-color: #e5e7eb;
        color: #374151;
    }

    .action-buttons {
        display: flex;
        gap: 0.75rem;
    }

    .action-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 6px;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
        color: #fff;
    }

    .view-btn {
        background-color: #3b82f6;
    }

    .view-btn:hover {
        background-color: #2563eb;
    }

    .edit-btn {
        background-color: #10b981;
    }

    .edit-btn:hover {
        background-color: #059669;
    }

    .delete-btn {
        background-color: #ef4444;
    }

    .delete-btn:hover {
        background-color: #dc2626;
    }

    .button-small {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    .dropdown {
        position: relative;
        display: inline-block;
    }

    .dropdown-toggle {
        cursor: pointer;
    }

    .dropdown-menu {
        position: absolute;
        right: 0;
        z-index: 10;
        min-width: 200px;
        background-color: #fff;
        border-radius: 0.25rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        display: none;
    }

    .dropdown:hover .dropdown-menu {
        display: block;
    }

    .dropdown-header {
        padding: 0.75rem 1rem;
        font-weight: 600;
        border-bottom: 1px solid #e5e7eb;
    }

    .dropdown-content {
        padding: 1rem;
    }

    .status-select {
        width: 100%;
        padding: 0.5rem;
        margin-bottom: 0.5rem;
        border: 1px solid #d1d5db;
        border-radius: 0.25rem;
    }

    .button-block {
        display: block;
        width: 100%;
    }

    .pagination {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
        margin-top: 1.5rem;
    }

    .pagination-item {
        display: inline-flex;
        align-items: center;
        padding: 0.5rem 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.25rem;
        color: #374151;
        text-decoration: none;
    }

    .pagination-item.active {
        background-color: #4f46e5;
        border-color: #4f46e5;
        color: #fff;
    }

    .pagination-item:hover:not(.active) {
        background-color: #f9fafb;
    }

    /* Modal styles */
    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        overflow: auto;
        padding: 2rem 1rem;
    }

    .modal-dialog {
        max-width: 450px;
        margin: 0 auto;
        position: relative;
    }

    .modal-content {
        background-color: #fff;
        border-radius: 0.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
        overflow: hidden;
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 1.25rem;
        border-bottom: 1px solid #e5e7eb;
    }

    .modal-header h3 {
        margin: 0;
        font-size: 1.125rem;
        font-weight: 600;
    }

    .close-button {
        background: none;
        border: none;
        font-size: 1.25rem;
        line-height: 1;
        color: #6b7280;
        cursor: pointer;
    }

    .modal-body {
        padding: 1.25rem;
    }

    .form-actions {
        display: flex;
        gap: 0.75rem;
        margin-top: 1.5rem;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize status update buttons
        const statusUpdateButtons = document.querySelectorAll('.status-update-btn');
        const statusUpdateModal = document.getElementById('statusUpdateModal');
        const closeButtons = document.querySelectorAll('[data-dismiss="modal"]');

        // Modal elements
        const previewName = document.getElementById('preview-name');
        const previewEmail = document.getElementById('preview-email');
        const previewProduct = document.getElementById('preview-product');
        const previewAmount = document.getElementById('preview-amount');
        const previewStatusBadge = document.getElementById('preview-status-badge');
        const modalApplicationId = document.getElementById('modal-application-id');
        const modalStatusId = document.getElementById('modal-status-id');

        // Handle status select change
        modalStatusId.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const statusClass = selectedOption.getAttribute('data-status');
            // Update the preview status badge when select changes
            previewStatusBadge.textContent = selectedOption.textContent;
            previewStatusBadge.className = 'status-badge status-' + statusClass;
        });

        // Open modal when clicking status update button
        statusUpdateButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                // Get application data from data attributes
                const id = this.getAttribute('data-id');
                const name = this.getAttribute('data-name');
                const email = this.getAttribute('data-email');
                const product = this.getAttribute('data-product');
                const amount = this.getAttribute('data-amount');
                const statusId = this.getAttribute('data-status-id');
                const status = this.getAttribute('data-status');

                // Set modal values
                previewName.textContent = name;
                previewEmail.textContent = email;
                previewProduct.textContent = product;
                previewAmount.textContent = amount;

                // Set status badge
                previewStatusBadge.textContent = status;
                previewStatusBadge.className = 'status-badge status-' + status.toLowerCase().replace(/\s+/g, '-');

                // Set form values
                modalApplicationId.value = id;
                modalStatusId.value = statusId;

                // Show modal
                statusUpdateModal.style.display = 'block';
            });
        });

        // Close modal when clicking close button or outside the modal
        closeButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                statusUpdateModal.style.display = 'none';
            });
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === statusUpdateModal) {
                statusUpdateModal.style.display = 'none';
            }
        });

        // Delete Application Modal Functionality
        const deleteButtons = document.querySelectorAll('.delete-application-btn');
        const deleteModal = document.getElementById('deleteConfirmModal');
        const deleteCloseButtons = deleteModal.querySelectorAll('[data-dismiss="modal"]');

        deleteButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const applicationId = this.getAttribute('data-id');
                const applicantName = this.getAttribute('data-name');
                const applicantEmail = this.getAttribute('data-email');
                const productName = this.getAttribute('data-product');
                const amount = this.getAttribute('data-amount');

                // Populate delete modal with application data
                document.getElementById('delete-preview-id').textContent = '#' + applicationId;
                document.getElementById('delete-preview-name').textContent = applicantName;
                document.getElementById('delete-preview-email').textContent = applicantEmail;
                document.getElementById('delete-preview-product').textContent = productName;
                document.getElementById('delete-preview-amount').textContent = amount;
                document.getElementById('delete-application-id').value = applicationId;

                // Show delete modal
                deleteModal.style.display = 'block';
            });
        });

        // Close delete modal
        deleteCloseButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                deleteModal.style.display = 'none';
            });
        });

        // Close delete modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === deleteModal) {
                deleteModal.style.display = 'none';
            }
        });
    });
</script>

<!-- Status Update Modal -->
<div class="modal" id="statusUpdateModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Update Application Status</h3>
                <button type="button" class="close-button" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="application-preview">
                    <div class="preview-header">Application Preview</div>
                    <div class="preview-content">
                        <div class="preview-row">
                            <div class="preview-label">Applicant:</div>
                            <div class="preview-value" id="preview-name"></div>
                        </div>
                        <div class="preview-row">
                            <div class="preview-label">Email:</div>
                            <div class="preview-value" id="preview-email"></div>
                        </div>
                        <div class="preview-row">
                            <div class="preview-label">Product:</div>
                            <div class="preview-value" id="preview-product"></div>
                        </div>
                        <div class="preview-row">
                            <div class="preview-label">Amount:</div>
                            <div class="preview-value" id="preview-amount"></div>
                        </div>
                        <div class="preview-row">
                            <div class="preview-label">Current Status:</div>
                            <div class="preview-value">
                                <span class="status-badge" id="preview-status-badge"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <form method="POST" action="applications.php" id="statusUpdateForm">
                    <?php echo csrf_token_field(); ?>
                    <input type="hidden" name="action" value="update_status">
                    <input type="hidden" name="application_id" id="modal-application-id">

                    <div class="form-group">
                        <label for="modal-status-id">New Status</label>
                        <div class="select-wrapper">
                            <select id="modal-status-id" name="status_id" class="form-control styled-select">
                                <?php foreach ($statuses as $status): ?>
                                    <option value="<?php echo $status['id']; ?>" data-status="<?php echo strtolower(str_replace(' ', '-', $status['name'])); ?>">
                                        <?php echo htmlspecialchars($status['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="select-arrow">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                    <path d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z"/>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="button button-primary">Update Status</button>
                        <button type="button" class="button button-secondary" data-dismiss="modal">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal" id="deleteConfirmModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h3>⚠️ Delete Application</h3>
                <button type="button" class="close-button" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="application-preview">
                    <div class="preview-header">⚠️ WARNING: This action cannot be undone!</div>
                    <div class="preview-content">
                        <div class="preview-row">
                            <div class="preview-label">Application ID:</div>
                            <div class="preview-value" id="delete-preview-id"></div>
                        </div>
                        <div class="preview-row">
                            <div class="preview-label">Applicant:</div>
                            <div class="preview-value" id="delete-preview-name"></div>
                        </div>
                        <div class="preview-row">
                            <div class="preview-label">Email:</div>
                            <div class="preview-value" id="delete-preview-email"></div>
                        </div>
                        <div class="preview-row">
                            <div class="preview-label">Product:</div>
                            <div class="preview-value" id="delete-preview-product"></div>
                        </div>
                        <div class="preview-row">
                            <div class="preview-label">Amount:</div>
                            <div class="preview-value" id="delete-preview-amount"></div>
                        </div>
                    </div>
                </div>

                <div class="delete-warning">
                    <h4>🗑️ The following will be permanently deleted:</h4>
                    <ul>
                        <li>✗ Application record and all form data</li>
                        <li>✗ All uploaded documents and files</li>
                        <li>✗ All related transactions</li>
                        <li>✗ All notifications related to this application</li>
                        <li>✗ Document access logs</li>
                    </ul>
                    <p><strong>This action cannot be undone!</strong></p>
                </div>

                <form method="POST" action="applications.php" id="deleteApplicationForm">
                    <?php echo csrf_token_field(); ?>
                    <input type="hidden" name="action" value="delete_application">
                    <input type="hidden" name="application_id" id="delete-application-id">

                    <div class="form-actions">
                        <button type="submit" class="button button-danger">🗑️ Yes, Delete Permanently</button>
                        <button type="button" class="button button-secondary" data-dismiss="modal">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
    /* Modal styles */
    .application-preview {
        background-color: #f9fafb;
        border-radius: 0.375rem;
        border: 1px solid #e5e7eb;
        margin-bottom: 1.5rem;
        overflow: hidden;
    }

    .preview-header {
        background-color: #f3f4f6;
        padding: 0.75rem 1rem;
        font-weight: 600;
        border-bottom: 1px solid #e5e7eb;
    }

    .preview-content {
        padding: 1rem;
    }

    .preview-row {
        display: flex;
        margin-bottom: 0.5rem;
    }

    .preview-row:last-child {
        margin-bottom: 0;
    }

    .preview-label {
        width: 35%;
        font-weight: 600;
        color: #6b7280;
    }

    .preview-value {
        width: 65%;
    }

    /* Styled Select */
    .select-wrapper {
        position: relative;
        width: 100%;
    }

    .styled-select {
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        width: 100%;
        padding: 0.75rem 1rem;
        font-size: 0.95rem;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        background-color: #fff;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .styled-select:focus {
        outline: none;
        border-color: #4f46e5;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }

    .select-arrow {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
        color: #6b7280;
    }

    /* Update modal buttons */
    .button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 1.5rem;
        font-size: 0.95rem;
        font-weight: 500;
        border-radius: 0.5rem;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .button-primary {
        background-color: #4f46e5;
        color: #fff;
    }

    .button-primary:hover {
        background-color: #4338ca;
    }

    .button-secondary {
        background-color: #f3f4f6;
        color: #374151;
        border: 1px solid #d1d5db;
    }

    .button-secondary:hover {
        background-color: #e5e7eb;
    }

    /* Delete Modal Styles */
    .delete-warning {
        background-color: #fef2f2;
        border: 1px solid #fecaca;
        border-radius: 0.375rem;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }

    .delete-warning h4 {
        color: #dc2626;
        margin-bottom: 0.75rem;
        font-size: 1rem;
    }

    .delete-warning ul {
        margin: 0.75rem 0;
        padding-left: 1.5rem;
    }

    .delete-warning li {
        color: #7f1d1d;
        margin-bottom: 0.25rem;
    }

    .delete-warning p {
        color: #dc2626;
        font-weight: 600;
        margin-bottom: 0;
    }

    .button-danger {
        background-color: #dc2626;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        cursor: pointer;
        font-weight: 600;
    }

    .button-danger:hover {
        background-color: #b91c1c;
    }
</style>

<?php
// Include admin footer
include '../includes/admin_footer.php';
?>
