<?php
/**
 * User Loans Page
 *
 * This file contains the loans page for users.
 *
 * @package LendSwift
 */

// Define LENDSWIFT constant to prevent direct access to included files
if (!defined('LENDSWIFT')) {
    define('LENDSWIFT', true);
}

// Include initialization file
if (!defined('LENDSWIFT')) {
    // If accessed directly, use relative path
    require_once '../includes/init.php';
}

// Check if the user is logged in
if (!is_user_logged_in()) {
    set_flash_message('error', 'Please log in to access your loans.');
    redirect(BASE_URL . '/?page=login');
}

// Get user information
$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['user_name'];
$user_email = $_SESSION['user_email'];

// Get database connection
$db = getDbConnection();

// Get user's loan applications
$loan_applications = [];
try {
    $stmt = $db->prepare("
        SELECT la.*, lp.name as product_name, ls.name as status_name, c.symbol as currency_symbol
        FROM loan_applications la
        LEFT JOIN loan_products lp ON la.loan_product_id = lp.id
        LEFT JOIN loan_statuses ls ON la.status_id = ls.id
        LEFT JOIN currencies c ON la.currency_id = c.id
        WHERE la.user_id = ?
        ORDER BY la.submission_date DESC
    ");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $loan_applications = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
} catch (Exception $e) {
    // Log the error
    error_log('Error fetching loan applications: ' . $e->getMessage());
}

// Get unread notifications count
$unread_notifications = 0;
try {
    $stmt = $db->prepare("
        SELECT COUNT(*) as count
        FROM notifications
        WHERE user_id = ? AND is_read = 0
    ");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $unread_notifications = $row['count'] ?? 0;
} catch (Exception $e) {
    // Log the error
    error_log('Error fetching unread notifications count: ' . $e->getMessage());
}

// Add dashboard-specific CSS and JS
add_style('dashboard.css');
add_script('dashboard.js');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Loans - <?php echo SITE_NAME; ?></title>

    <!-- Favicon -->
    <link rel="icon" href="<?php echo ASSETS_URL; ?>/images/favicon.svg">

    <!-- CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/dashboard.css">
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/sidebar-fix.css">

    <!-- Font Awesome (for icons) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="dashboard-layout">
        <!-- Sidebar -->
        <aside class="dashboard-sidebar" id="dashboard-sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <?php
                    // Get site logo and size settings
                    $site_logo = get_setting('site_logo', '/assets/images/logo.svg');
                    $logo_size = get_setting('logo_size', 'medium');
                    $logo_size_class = 'logo-' . $logo_size;
                    $logo_custom_size = get_setting('logo_custom_size', '100');
                    $show_site_name = get_setting('show_site_name', '0');

                    // Calculate custom size factor (percentage to decimal)
                    $size_factor = intval($logo_custom_size) / 100;

                    if (!empty($site_logo) && file_exists(BASE_PATH . $site_logo)) {
                        // If logo exists, show it with custom size
                        echo '<img src="' . BASE_URL . $site_logo . '" alt="' . SITE_NAME . '" class="' . $logo_size_class . '" style="--logo-size-factor: ' . $size_factor . ';">';

                        // Show site name if enabled
                        if ($show_site_name == '1') {
                            echo '<span class="sidebar-logo-text">' . SITE_NAME . '</span>';
                        }
                    } else {
                        // If no logo, show site name
                        echo '<span class="sidebar-logo-text">' . SITE_NAME . '</span>';
                    }
                    ?>
                </div>
            </div>

            <div class="sidebar-nav">
                <div class="sidebar-nav-title">Main</div>
                <ul class="sidebar-nav-items">
                    <li class="sidebar-nav-item">
                        <a href="<?php echo BASE_URL; ?>/?page=dashboard" class="sidebar-nav-link">
                            <span class="sidebar-nav-icon"><i class="fas fa-tachometer-alt"></i></span>
                            Dashboard
                        </a>
                    </li>
                    <li class="sidebar-nav-item">
                        <a href="<?php echo BASE_URL; ?>/?page=loan-application" class="sidebar-nav-link">
                            <span class="sidebar-nav-icon"><i class="fas fa-file-alt"></i></span>
                            Apply for Loan
                        </a>
                    </li>
                    <li class="sidebar-nav-item">
                        <a href="<?php echo BASE_URL; ?>/?page=loans" class="sidebar-nav-link active">
                            <span class="sidebar-nav-icon"><i class="fas fa-money-bill-wave"></i></span>
                            My Loans
                        </a>
                    </li>
                    <li class="sidebar-nav-item">
                        <a href="<?php echo BASE_URL; ?>/?page=transactions" class="sidebar-nav-link">
                            <span class="sidebar-nav-icon"><i class="fas fa-exchange-alt"></i></span>
                            Transactions
                        </a>
                    </li>
                </ul>

                <div class="sidebar-nav-title">Account</div>
                <ul class="sidebar-nav-items">
                    <li class="sidebar-nav-item">
                        <a href="<?php echo BASE_URL; ?>/?page=documents" class="sidebar-nav-link">
                            <span class="sidebar-nav-icon"><i class="fas fa-file-upload"></i></span>
                            Documents
                        </a>
                    </li>
                    <li class="sidebar-nav-item">
                        <a href="<?php echo BASE_URL; ?>/?page=document-history" class="sidebar-nav-link">
                            <span class="sidebar-nav-icon"><i class="fas fa-history"></i></span>
                            Document History
                        </a>
                    </li>
                    <li class="sidebar-nav-item">
                        <a href="<?php echo BASE_URL; ?>/?page=profile" class="sidebar-nav-link">
                            <span class="sidebar-nav-icon"><i class="fas fa-user"></i></span>
                            My Profile
                        </a>
                    </li>
                    <li class="sidebar-nav-item">
                        <a href="<?php echo BASE_URL; ?>/?page=notifications" class="sidebar-nav-link">
                            <span class="sidebar-nav-icon"><i class="fas fa-bell"></i></span>
                            Notifications
                            <?php if ($unread_notifications > 0): ?>
                                <span class="badge badge-primary"><?php echo $unread_notifications; ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                </ul>

                <div class="sidebar-nav-title">Tools</div>
                <ul class="sidebar-nav-items">
                    <li class="sidebar-nav-item">
                        <a href="<?php echo BASE_URL; ?>/?page=loan-calculator" class="sidebar-nav-link">
                            <span class="sidebar-nav-icon"><i class="fas fa-calculator"></i></span>
                            Loan Calculator
                        </a>
                    </li>
                    <li class="sidebar-nav-item">
                        <a href="<?php echo BASE_URL; ?>/?page=support" class="sidebar-nav-link">
                            <span class="sidebar-nav-icon"><i class="fas fa-headset"></i></span>
                            Support
                        </a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-footer">
                <div class="sidebar-user">
                    <div class="sidebar-user-avatar">
                        <?php echo strtoupper(substr($user_name, 0, 1)); ?>
                    </div>
                    <div class="sidebar-user-info">
                        <div class="sidebar-user-name"><?php echo htmlspecialchars($user_name); ?></div>
                        <div class="sidebar-user-email"><?php echo htmlspecialchars($user_email); ?></div>
                    </div>
                </div>
                <a href="<?php echo BASE_URL; ?>/?page=logout" class="sidebar-logout">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="dashboard-main">
            <!-- Header -->
            <header class="dashboard-header">
                <button id="mobile-sidebar-toggle" class="mobile-sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>

                <h1 class="header-title">My Loans</h1>

                <div class="header-actions">
                    <div class="header-action">
                        <button class="header-action-button" id="notifications-toggle">
                            <i class="fas fa-bell"></i>
                            <?php if ($unread_notifications > 0): ?>
                                <span class="header-action-badge"><?php echo $unread_notifications; ?></span>
                            <?php endif; ?>
                        </button>
                    </div>

                    <div class="header-action">
                        <a href="<?php echo BASE_URL; ?>/?page=profile" class="header-action-button">
                            <i class="fas fa-user"></i>
                        </a>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="dashboard-content">
                <div class="content-header">
                    <h2 class="content-title">My Loan Applications</h2>
                    <p class="content-subtitle">View and manage all your loan applications.</p>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">All Loan Applications</h3>
                        <div class="card-actions">
                            <a href="<?php echo BASE_URL; ?>/?page=loan-application" class="auth-button">
                                <i class="fas fa-plus"></i> New Application
                            </a>
                        </div>
                    </div>

                    <?php if (empty($loan_applications)): ?>
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <h4 class="empty-state-title">No loans yet</h4>
                            <p class="empty-state-description">You haven't applied for any loans yet. Start by applying for a loan.</p>
                            <a href="<?php echo BASE_URL; ?>/?page=loan-application" class="auth-button">
                                <i class="fas fa-plus"></i> Apply for Loan
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Product</th>
                                        <th>Amount</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($loan_applications as $application): ?>
                                        <tr>
                                            <td>#<?php echo $application['id']; ?></td>
                                            <td><?php echo htmlspecialchars($application['product_name']); ?></td>
                                            <td><?php echo $application['currency_symbol'] . number_format($application['applied_amount'], 2); ?></td>
                                            <td><?php echo date('M d, Y', strtotime($application['submission_date'])); ?></td>
                                            <td>
                                                <?php
                                                $status_class = 'status-badge-primary';
                                                $status_name = strtolower($application['status_name']);
                                                if ($status_name === 'pending') {
                                                    $status_class = 'status-badge-warning';
                                                } elseif ($status_name === 'active' || $status_name === 'approved') {
                                                    $status_class = 'status-badge-success';
                                                } elseif ($status_name === 'completed') {
                                                    $status_class = 'status-badge-info';
                                                } elseif ($status_name === 'rejected' || $status_name === 'declined') {
                                                    $status_class = 'status-badge-danger';
                                                }
                                                ?>
                                                <span class="status-badge <?php echo $status_class; ?>">
                                                    <?php echo htmlspecialchars($application['status_name']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="<?php echo BASE_URL; ?>/?page=loan-details&id=<?php echo $application['id']; ?>" class="table-action" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?php echo BASE_URL; ?>/?page=documents&application_id=<?php echo $application['id']; ?>" class="table-action" title="Documents">
                                                    <i class="fas fa-file-alt"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Minimal Footer -->
            <footer class="dashboard-footer">
                <div class="footer-content">
                    <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. All rights reserved.</p>
                </div>
            </footer>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="<?php echo ASSETS_URL; ?>/js/dashboard.js"></script>
</body>
</html>