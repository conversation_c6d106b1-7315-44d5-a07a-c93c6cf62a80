<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="400" viewBox="0 0 600 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Shapes -->
  <circle cx="300" cy="200" r="180" fill="#F3F4FF" />
  <circle cx="150" cy="100" r="50" fill="#E0E7FF" />
  <circle cx="450" cy="300" r="70" fill="#EEF2FF" />
  
  <!-- Company Building -->
  <g transform="translate(250, 120)">
    <!-- Building Base -->
    <rect x="-100" y="0" width="200" height="160" rx="8" fill="white" stroke="#4F46E5" stroke-width="2" />
    
    <!-- Windows -->
    <g transform="translate(-80, 20)">
      <rect width="20" height="30" rx="2" fill="#E0E7FF" />
      <rect x="40" width="20" height="30" rx="2" fill="#E0E7FF" />
      <rect x="80" width="20" height="30" rx="2" fill="#E0E7FF" />
      <rect x="120" width="20" height="30" rx="2" fill="#E0E7FF" />
      <rect y="50" width="20" height="30" rx="2" fill="#E0E7FF" />
      <rect x="40" y="50" width="20" height="30" rx="2" fill="#E0E7FF" />
      <rect x="80" y="50" width="20" height="30" rx="2" fill="#E0E7FF" />
      <rect x="120" y="50" width="20" height="30" rx="2" fill="#E0E7FF" />
      <rect y="100" width="20" height="30" rx="2" fill="#E0E7FF" />
      <rect x="40" y="100" width="20" height="30" rx="2" fill="#E0E7FF" />
      <rect x="120" y="100" width="20" height="30" rx="2" fill="#E0E7FF" />
    </g>
    
    <!-- Door -->
    <rect x="-20" y="100" width="40" height="60" rx="4" fill="#4F46E5" />
    <rect x="-10" y="110" width="20" height="40" rx="2" fill="#E0E7FF" />
    
    <!-- Roof -->
    <path d="M-110 0L0 -50L110 0" stroke="#4F46E5" stroke-width="3" fill="#F3F4FF" />
    
    <!-- Flag -->
    <rect x="0" y="-70" width="2" height="30" fill="#4F46E5" />
    <path d="M2 -70L20 -65L2 -60Z" fill="#4F46E5" />
  </g>
  
  <!-- People -->
  <g transform="translate(150, 280)">
    <!-- Person 1 -->
    <circle cx="0" cy="0" r="15" fill="#4F46E5" />
    <path d="M0 -5C3 -5 5 -8 5 -12C5 -16 3 -18 0 -18C-3 -18 -5 -16 -5 -12C-5 -8 -3 -5 0 -5Z" fill="#E0E7FF" />
    <path d="M-8 0C-8 -8 8 -8 8 0" fill="#4F46E5" />
  </g>
  
  <g transform="translate(180, 290)">
    <!-- Person 2 -->
    <circle cx="0" cy="0" r="12" fill="#4F46E5" />
    <path d="M0 -4C2.5 -4 4 -6.5 4 -10C4 -13.5 2.5 -15 0 -15C-2.5 -15 -4 -13.5 -4 -10C-4 -6.5 -2.5 -4 0 -4Z" fill="#E0E7FF" />
    <path d="M-6 0C-6 -6 6 -6 6 0" fill="#4F46E5" />
  </g>
  
  <g transform="translate(350, 280)">
    <!-- Person 3 -->
    <circle cx="0" cy="0" r="15" fill="#4F46E5" />
    <path d="M0 -5C3 -5 5 -8 5 -12C5 -16 3 -18 0 -18C-3 -18 -5 -16 -5 -12C-5 -8 -3 -5 0 -5Z" fill="#E0E7FF" />
    <path d="M-8 0C-8 -8 8 -8 8 0" fill="#4F46E5" />
  </g>
  
  <g transform="translate(380, 290)">
    <!-- Person 4 -->
    <circle cx="0" cy="0" r="12" fill="#4F46E5" />
    <path d="M0 -4C2.5 -4 4 -6.5 4 -10C4 -13.5 2.5 -15 0 -15C-2.5 -15 -4 -13.5 -4 -10C-4 -6.5 -2.5 -4 0 -4Z" fill="#E0E7FF" />
    <path d="M-6 0C-6 -6 6 -6 6 0" fill="#4F46E5" />
  </g>
  
  <!-- Mission/Values Icons -->
  <g transform="translate(120, 180)">
    <!-- Target/Mission Icon -->
    <circle cx="0" cy="0" r="25" fill="white" stroke="#4F46E5" stroke-width="2" />
    <circle cx="0" cy="0" r="18" fill="none" stroke="#4F46E5" stroke-width="2" stroke-dasharray="2 2" />
    <circle cx="0" cy="0" r="10" fill="#4F46E5" />
    <circle cx="0" cy="0" r="5" fill="white" />
    <line x1="-30" y1="0" x2="-25" y2="0" stroke="#4F46E5" stroke-width="2" />
    <line x1="25" y1="0" x2="30" y2="0" stroke="#4F46E5" stroke-width="2" />
    <line x1="0" y1="-30" x2="0" y2="-25" stroke="#4F46E5" stroke-width="2" />
    <line x1="0" y1="25" x2="0" y2="30" stroke="#4F46E5" stroke-width="2" />
  </g>
  
  <g transform="translate(450, 180)">
    <!-- Values Icon -->
    <rect x="-20" y="-20" width="40" height="40" rx="8" fill="white" stroke="#4F46E5" stroke-width="2" />
    <path d="M-10 -5L-5 0L5 -10M-10 5L-5 10L15 -10" stroke="#4F46E5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
  </g>
  
  <!-- Decorative Elements -->
  <circle cx="120" cy="120" r="10" fill="#4F46E5" />
  <circle cx="480" cy="100" r="8" fill="#4F46E5" />
  <circle cx="500" cy="350" r="12" fill="#4F46E5" />
  <circle cx="150" cy="350" r="6" fill="#4F46E5" />
  
  <!-- Connection Lines -->
  <path d="M150 280L250 250" stroke="#4F46E5" stroke-width="2" stroke-dasharray="4 4" />
  <path d="M350 280L250 250" stroke="#4F46E5" stroke-width="2" stroke-dasharray="4 4" />
  <path d="M120 180L200 150" stroke="#4F46E5" stroke-width="2" stroke-dasharray="4 4" />
  <path d="M450 180L300 150" stroke="#4F46E5" stroke-width="2" stroke-dasharray="4 4" />
</svg>
