<?php
/**
 * Guest Conversion Management
 * Dedicated interface for managing guest-to-user conversions
 */

// Define LENDSWIFT constant
if (!defined('LENDSWIFT')) {
    define('LENDSWIFT', true);
}

// Include initialization
require_once __DIR__ . '/../includes/init.php';
require_once __DIR__ . '/../includes/guest_account_functions.php';
require_once __DIR__ . '/../includes/email_functions.php';

// Check admin authentication
if (!is_admin_logged_in()) {
    set_flash_message('error', 'Please log in to access the admin dashboard.');
    redirect(BASE_URL . '/admin/login.php');
}

$db = getDbConnection();

// Handle manual conversion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        set_flash_message('error', 'Invalid form submission.');
        redirect(BASE_URL . '/admin/guest-conversion.php');
    }
    
    if ($_POST['action'] === 'convert_guest') {
        $application_id = (int)$_POST['application_id'];
        
        try {
            // Manual conversion function
            $result = manual_convert_guest_to_user($application_id);
            
            if ($result['success']) {
                set_flash_message('success', "Guest application #{$application_id} converted successfully! User ID: {$result['user_id']}, Password: {$result['password']}");
            } else {
                set_flash_message('error', "Conversion failed: " . $result['error']);
            }
        } catch (Exception $e) {
            set_flash_message('error', 'Conversion error: ' . $e->getMessage());
        }
        
        redirect(BASE_URL . '/admin/guest-conversion.php');
    }
}

// Get all guest applications
$guest_applications = [];
$result = $db->query("
    SELECT la.*, ls.name as status_name, lp.name as product_name
    FROM loan_applications la
    LEFT JOIN loan_statuses ls ON la.status_id = ls.id
    LEFT JOIN loan_products lp ON la.loan_product_id = lp.id
    WHERE la.is_guest_application = 1
    ORDER BY la.submission_date DESC
");

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $guest_applications[] = $row;
    }
}

// Manual conversion function
function manual_convert_guest_to_user($application_id) {
    $db = getDbConnection();
    
    try {
        $db->begin_transaction();
        
        // Get guest application
        $stmt = $db->prepare("SELECT * FROM loan_applications WHERE id = ? AND is_guest_application = 1");
        $stmt->bind_param("i", $application_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            throw new Exception('Guest application not found');
        }
        
        $application = $result->fetch_assoc();
        
        // Check if email exists
        $stmt = $db->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->bind_param("s", $application['guest_email']);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            throw new Exception('User with this email already exists');
        }
        
        // Generate password
        $password = 'Guest' . rand(1000, 9999) . '!';
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        
        // Create user
        $currency_id = $application['currency_id'] ?? 1;
        $stmt = $db->prepare("
            INSERT INTO users (name, email, phone, password, currency_id, status, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, 'active', NOW(), NOW())
        ");
        $stmt->bind_param("sssii", 
            $application['guest_name'],
            $application['guest_email'],
            $application['guest_phone'],
            $hashed_password,
            $currency_id
        );
        
        if (!$stmt->execute()) {
            throw new Exception('Failed to create user: ' . $db->error);
        }
        
        $user_id = $db->insert_id;
        
        // Update application
        $stmt = $db->prepare("
            UPDATE loan_applications 
            SET user_id = ?, guest_name = NULL, guest_email = NULL, guest_phone = NULL, is_guest_application = 0
            WHERE id = ?
        ");
        $stmt->bind_param("ii", $user_id, $application_id);
        $stmt->execute();
        
        // Update documents
        $stmt = $db->prepare("UPDATE application_documents SET user_id = ? WHERE application_id = ?");
        $stmt->bind_param("ii", $user_id, $application_id);
        $stmt->execute();
        
        // Update form data
        $stmt = $db->prepare("UPDATE form_data SET user_id = ? WHERE application_id = ?");
        $stmt->bind_param("ii", $user_id, $application_id);
        $stmt->execute();
        
        $db->commit();
        
        // Send welcome email
        send_conversion_welcome_email($user_id, $password);
        
        return [
            'success' => true,
            'user_id' => $user_id,
            'password' => $password,
            'email' => $application['guest_email']
        ];
        
    } catch (Exception $e) {
        $db->rollback();
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// Email function for converted users
function send_conversion_welcome_email($user_id, $password) {
    $db = getDbConnection();
    
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) return false;
    
    $user = $result->fetch_assoc();
    
    // Get dynamic company/site name from settings
    $company_settings = [];
    $company_result = $db->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('company_name', 'site_name', 'email_from_name')");
    if ($company_result && $company_result->num_rows > 0) {
        while ($row = $company_result->fetch_assoc()) {
            $company_settings[$row['setting_key']] = $row['setting_value'];
        }
    }
    $site_name = $company_settings['site_name'] ?? $company_settings['company_name'] ?? 'LendSwift';

    // Get site logo for email template - use dynamic logo from database
    $site_logo = get_setting('site_logo', '/assets/images/logo.svg');
    $logo_url = BASE_URL . $site_logo;
    $logo_exists = !empty($site_logo) && file_exists(BASE_PATH . $site_logo);

    $subject = "Welcome to " . $site_name . " - Your Account is Ready!";
    $body = "
    <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;'>
        <div style='text-align: center; margin-bottom: 20px;'>";

    // Add logo or company name fallback
    if ($logo_exists) {
        $body .= "
            <img src='" . $logo_url . "' alt='" . htmlspecialchars($site_name) . " Logo' style='max-width: 200px; max-height: 60px;'>";
    } else {
        $body .= "
            <div style='font-size: 24px; font-weight: bold; color: #4f46e5; margin: 10px 0;'>" . htmlspecialchars($site_name) . "</div>";
    }

    $body .= "
        </div>
        <h1 style='color: #4f46e5;'>Welcome to " . $site_name . "!</h1>
        <p>Dear " . htmlspecialchars($user['name']) . ",</p>
        <p>Your loan application has been approved and we've created your account!</p>
        <div style='background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;'>
            <h3>Your Login Credentials:</h3>
            <p><strong>Email:</strong> " . htmlspecialchars($user['email']) . "</p>
            <p><strong>Password:</strong> " . htmlspecialchars($password) . "</p>
        </div>
        <p><a href='" . BASE_URL . "/?page=login' style='background: #4f46e5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;'>Login to Your Account</a></p>
        <p>Please change your password after your first login.</p>
    </div>";
    
    return send_email($user['email'], $subject, $body);
}

include '../includes/admin_header.php';
?>

<div class="guest-conversion-page">
    <div class="page-header">
        <h1>Guest Conversion Management</h1>
        <p>Manage guest-to-user account conversions</p>
    </div>

    <?php if (!empty($guest_applications)): ?>
    <div class="card">
        <div class="card-header">
            <h2>Guest Applications (<?php echo count($guest_applications); ?>)</h2>
        </div>
        <div class="card-content">
            <div class="table-responsive">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Guest Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Product</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($guest_applications as $app): ?>
                        <tr>
                            <td>#<?php echo $app['id']; ?></td>
                            <td><?php echo htmlspecialchars($app['guest_name']); ?></td>
                            <td><?php echo htmlspecialchars($app['guest_email']); ?></td>
                            <td><?php echo htmlspecialchars($app['guest_phone']); ?></td>
                            <td><?php echo htmlspecialchars($app['product_name']); ?></td>
                            <td>$<?php echo number_format($app['applied_amount'], 2); ?></td>
                            <td>
                                <span class="status-badge status-<?php echo strtolower(str_replace(' ', '-', $app['status_name'])); ?>">
                                    <?php echo htmlspecialchars($app['status_name']); ?>
                                </span>
                            </td>
                            <td><?php echo date('M d, Y', strtotime($app['submission_date'])); ?></td>
                            <td>
                                <div class="action-buttons">
                                    <a href="<?php echo BASE_URL; ?>/admin/application-details.php?id=<?php echo $app['id']; ?>" 
                                       class="button button-small">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    
                                    <?php if ($app['status_id'] == 3): // Approved ?>
                                    <form method="POST" style="display: inline;" 
                                          onsubmit="return confirm('Convert guest application #<?php echo $app['id']; ?> to user account?');">
                                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                        <input type="hidden" name="action" value="convert_guest">
                                        <input type="hidden" name="application_id" value="<?php echo $app['id']; ?>">
                                        <button type="submit" class="button button-small button-success">
                                            <i class="fas fa-user-plus"></i> Convert
                                        </button>
                                    </form>
                                    <?php else: ?>
                                    <span class="text-muted">Approve first</span>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php else: ?>
    <div class="card">
        <div class="card-content">
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3>No Guest Applications</h3>
                <p>There are no guest applications requiring conversion.</p>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<style>
.guest-conversion-page {
    margin-bottom: 2rem;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.action-buttons form {
    margin: 0;
}

.text-muted {
    color: #6b7280;
    font-size: 0.875rem;
}
</style>

<?php include '../includes/admin_footer.php'; ?>
