/**
 * Notification Banner System
 * Provides temporary visual alerts for new notifications
 */

/* Banner Container */
.notification-banner {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 9999;
    padding: 12px 20px;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-100%);
    transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
    opacity: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

/* Banner States */
.notification-banner.show {
    transform: translateY(0);
    opacity: 1;
}

.notification-banner.hide {
    transform: translateY(-100%);
    opacity: 0;
}

/* User Banner (Light Green) */
.notification-banner.user {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border-bottom: 2px solid #b8dacc;
}

/* Admin Banner (Red) */
.notification-banner.admin {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border-bottom: 2px solid #f1b0b7;
}

/* Banner Icon */
.notification-banner-icon {
    font-size: 16px;
    margin-right: 4px;
}

/* Banner Message */
.notification-banner-message {
    flex: 1;
    margin: 0;
}

/* Banner Action Button */
.notification-banner-action {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: inherit;
    padding: 4px 12px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.2s ease;
    margin-left: 12px;
}

.notification-banner-action:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    color: inherit;
    text-decoration: none;
}

/* Close Button */
.notification-banner-close {
    background: none;
    border: none;
    color: inherit;
    font-size: 16px;
    cursor: pointer;
    padding: 4px;
    margin-left: 8px;
    border-radius: 2px;
    transition: background-color 0.2s ease;
}

.notification-banner-close:hover {
    background: rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .notification-banner {
        padding: 10px 16px;
        font-size: 13px;
        flex-direction: column;
        gap: 6px;
        text-align: center;
    }
    
    .notification-banner-action {
        margin-left: 0;
        margin-top: 4px;
    }
    
    .notification-banner-close {
        position: absolute;
        top: 8px;
        right: 12px;
        margin: 0;
    }
}

/* Animation for multiple banners */
.notification-banner + .notification-banner {
    top: 60px;
}

/* Ensure content doesn't get hidden behind banner */
body.has-notification-banner {
    padding-top: 60px;
    transition: padding-top 0.3s ease-in-out;
}

body.has-notification-banner .dashboard-layout {
    margin-top: 0;
}

/* Special handling for admin layout */
body.has-notification-banner.admin-layout {
    padding-top: 60px;
}

/* Fade in animation */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-100%);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Fade out animation */
@keyframes fadeOutUp {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-100%);
    }
}

.notification-banner.animate-in {
    animation: fadeInDown 0.3s ease-out forwards;
}

.notification-banner.animate-out {
    animation: fadeOutUp 0.3s ease-in forwards;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .notification-banner.user {
        background: #d4edda;
        color: #000;
        border-bottom: 3px solid #155724;
    }
    
    .notification-banner.admin {
        background: #f8d7da;
        color: #000;
        border-bottom: 3px solid #721c24;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .notification-banner {
        transition: none;
    }
    
    .notification-banner.animate-in,
    .notification-banner.animate-out {
        animation: none;
    }
}
