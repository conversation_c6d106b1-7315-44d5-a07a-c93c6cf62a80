/**
 * Mobile Fix CSS
 *
 * This file contains styles to fix mobile-specific issues.
 *
 * @package LendSwift
 */

/* Mobile menu and navigation fixes */
@media (max-width: 768px) {
    /* Mobile menu toggle button styling */
    .mobile-menu-toggle {
        background-color: var(--primary-color) !important;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* Fix for mobile hero title */
    .mobile-hero-title {
        color: var(--text-color) !important;
        -webkit-text-fill-color: var(--text-color) !important;
        background: none !important;
        -webkit-background-clip: initial !important;
        background-clip: initial !important;
        text-shadow: none !important;
    }

    /* Hide any fixed positioned elements on the right side */
    body > *:not(.mobile-app-nav):not(.mobile-menu):not(.mobile-menu-overlay):not(.header):not(.container):not(.home-wrapper) {
        display: none !important;
    }

    /* Popup mobile menu styling */
    .mobile-menu {
        position: fixed !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) scale(0.9) !important;
        width: 90% !important;
        max-width: 320px !important;
        max-height: 80vh !important;
        background-color: white !important;
        z-index: 1010 !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2) !important;
        transition: transform 0.3s ease, opacity 0.3s ease !important;
        padding: 1.5rem !important;
        overflow-y: auto !important;
        opacity: 0 !important;
        border-radius: 12px !important;
        display: none !important;
    }

    /* Active state for popup menu */
    .mobile-menu.active,
    body.mobile-menu-active .mobile-menu {
        display: block !important;
        opacity: 1 !important;
        transform: translate(-50%, -50%) scale(1) !important;
    }
}
