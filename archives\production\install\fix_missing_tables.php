<?php
/**
 * Fix Missing Tables Script
 * 
 * This script checks for missing tables in the database and creates them if needed.
 * It also provides a way to update database credentials if they are incorrect.
 */

// Start session
session_start();

// Define constants
define('INSTALLER_PATH', __DIR__);
define('ROOT_PATH', dirname(__DIR__));
define('CONFIG_PATH', ROOT_PATH . '/includes/core/config.php');
define('SQL_FILE', INSTALLER_PATH . '/database.sql');

// Default database settings
$db_settings = [
    'host' => 'localhost',
    'name' => 'loan',
    'user' => 'root',
    'pass' => '',
    'charset' => 'utf8mb4'
];

// Get current database settings from config file
$config_content = file_get_contents(CONFIG_PATH);
if ($config_content) {
    // Extract database settings from config file
    preg_match("/define\('DB_HOST', '(.*?)'\);/", $config_content, $host_match);
    preg_match("/define\('DB_USER', '(.*?)'\);/", $config_content, $user_match);
    preg_match("/define\('DB_PASS', '(.*?)'\);/", $config_content, $pass_match);
    preg_match("/define\('DB_NAME', '(.*?)'\);/", $config_content, $name_match);
    
    if (!empty($host_match[1])) $db_settings['host'] = $host_match[1];
    if (!empty($user_match[1])) $db_settings['user'] = $user_match[1];
    if (!empty($pass_match[1])) $db_settings['pass'] = $pass_match[1];
    if (!empty($name_match[1])) $db_settings['name'] = $name_match[1];
}

// Process form submission
$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$error = '';
$success = '';
$connection_status = false;
$tables = [];
$missing_tables = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step === 1 && isset($_POST['db_settings'])) {
        // Update database settings
        $db_settings = [
            'host' => trim($_POST['db_host']),
            'name' => trim($_POST['db_name']),
            'user' => trim($_POST['db_user']),
            'pass' => $_POST['db_pass'],
            'charset' => 'utf8mb4'
        ];
        
        // Test database connection
        try {
            $mysqli = new mysqli(
                $db_settings['host'],
                $db_settings['user'],
                $db_settings['pass']
            );
            
            if ($mysqli->connect_error) {
                throw new Exception("Database connection failed: " . $mysqli->connect_error);
            }
            
            // Update config file with new credentials
            $config_content = file_get_contents(CONFIG_PATH);
            if (!$config_content) {
                throw new Exception("Could not read configuration file.");
            }
            
            // Replace database settings
            $config_content = preg_replace(
                "/define\('DB_HOST', '.*?'\);/",
                "define('DB_HOST', '{$db_settings['host']}');",
                $config_content
            );
            $config_content = preg_replace(
                "/define\('DB_USER', '.*?'\);/",
                "define('DB_USER', '{$db_settings['user']}');",
                $config_content
            );
            $config_content = preg_replace(
                "/define\('DB_PASS', '.*?'\);/",
                "define('DB_PASS', '{$db_settings['pass']}');",
                $config_content
            );
            $config_content = preg_replace(
                "/define\('DB_NAME', '.*?'\);/",
                "define('DB_NAME', '{$db_settings['name']}');",
                $config_content
            );
            
            // Write updated config file
            if (file_put_contents(CONFIG_PATH, $config_content) === false) {
                throw new Exception("Could not write to configuration file.");
            }
            
            $success = "Database credentials updated successfully!";
            $step = 2;
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    } elseif ($step === 2 && isset($_POST['fix_tables'])) {
        // Fix missing tables
        try {
            $mysqli = new mysqli(
                $db_settings['host'],
                $db_settings['user'],
                $db_settings['pass']
            );
            
            if ($mysqli->connect_error) {
                throw new Exception("Database connection failed: " . $mysqli->connect_error);
            }
            
            // Create database if it doesn't exist
            $mysqli->query("CREATE DATABASE IF NOT EXISTS `{$db_settings['name']}` CHARACTER SET {$db_settings['charset']}");
            
            if ($mysqli->error) {
                throw new Exception("Failed to create database: " . $mysqli->error);
            }
            
            // Select the database
            $mysqli->select_db($db_settings['name']);
            
            // Get list of tables to fix
            $tables_to_fix = isset($_POST['tables']) ? $_POST['tables'] : [];
            
            if (empty($tables_to_fix)) {
                throw new Exception("No tables selected to fix.");
            }
            
            // Import SQL file
            $sql_content = file_get_contents(SQL_FILE);
            if (!$sql_content) {
                throw new Exception("Could not read SQL file.");
            }
            
            // Extract CREATE TABLE statements for selected tables
            $fixed_tables = [];
            $errors = [];
            
            foreach ($tables_to_fix as $table) {
                // Find CREATE TABLE statement for this table
                if (preg_match('/CREATE TABLE\s+`' . preg_quote($table, '/') . '`\s+\(.*?\)\s+ENGINE.*?;/is', $sql_content, $matches)) {
                    $create_statement = $matches[0];
                    
                    // Execute CREATE TABLE statement
                    $result = $mysqli->query($create_statement);
                    
                    if ($result) {
                        $fixed_tables[] = $table;
                    } else {
                        $errors[] = "Failed to create table '{$table}': " . $mysqli->error;
                    }
                } else {
                    $errors[] = "Could not find CREATE TABLE statement for '{$table}' in SQL file.";
                }
            }
            
            if (!empty($fixed_tables)) {
                $success = "Successfully created the following tables: " . implode(", ", $fixed_tables);
            }
            
            if (!empty($errors)) {
                $error = "Some tables could not be created:<br>" . implode("<br>", $errors);
            }
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// Check database connection and get table list
try {
    $mysqli = new mysqli(
        $db_settings['host'],
        $db_settings['user'],
        $db_settings['pass']
    );
    
    if (!$mysqli->connect_error) {
        $connection_status = true;
        
        // Check if database exists
        $result = $mysqli->query("SHOW DATABASES LIKE '{$db_settings['name']}'");
        if ($result && $result->num_rows > 0) {
            // Select the database
            $mysqli->select_db($db_settings['name']);
            
            // Get list of existing tables
            $result = $mysqli->query("SHOW TABLES");
            if ($result) {
                while ($row = $result->fetch_row()) {
                    $tables[] = $row[0];
                }
            }
            
            // Get list of expected tables from SQL file
            $sql_content = file_get_contents(SQL_FILE);
            if ($sql_content) {
                preg_match_all('/CREATE TABLE\s+`(.*?)`/i', $sql_content, $matches);
                if (!empty($matches[1])) {
                    $expected_tables = $matches[1];
                    $missing_tables = array_diff($expected_tables, $tables);
                }
            }
        }
    }
} catch (Exception $e) {
    // Ignore connection errors here, they will be handled in the form
}

// HTML header
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Missing Tables</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        h1, h2 {
            color: #333;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"],
        input[type="password"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .form-hint {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .btn {
            display: inline-block;
            background-color: #4F46E5;
            color: #fff;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
        }
        .btn:hover {
            background-color: #4338CA;
        }
        .error {
            color: #e74c3c;
            background-color: #fdf7f7;
            border: 1px solid #e74c3c;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .success {
            color: #2ecc71;
            background-color: #f7fdf7;
            border: 1px solid #2ecc71;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .warning {
            color: #f39c12;
            background-color: #fef9e7;
            border: 1px solid #f39c12;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .status-box {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .checkbox-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
        }
        .checkbox-item {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Fix Missing Tables</h1>
        
        <?php if (!empty($error)): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if (!empty($success)): ?>
            <div class="success"><?php echo $success; ?></div>
        <?php endif; ?>
        
        <?php if ($step === 1): ?>
            <?php if (!$connection_status): ?>
                <div class="warning">
                    <p>Could not connect to the database with the current credentials. Please update your database settings below.</p>
                </div>
            <?php endif; ?>
            
            <h2>Database Configuration</h2>
            <p>Please enter your database connection details below:</p>
            
            <form method="POST" action="<?php echo $_SERVER['PHP_SELF']; ?>?step=1">
                <input type="hidden" name="db_settings" value="1">
                
                <div class="form-group">
                    <label for="db_host">Database Host</label>
                    <input type="text" id="db_host" name="db_host" value="<?php echo htmlspecialchars($db_settings['host']); ?>" required>
                    <div class="form-hint">Usually "localhost" or "127.0.0.1"</div>
                </div>
                
                <div class="form-group">
                    <label for="db_name">Database Name</label>
                    <input type="text" id="db_name" name="db_name" value="<?php echo htmlspecialchars($db_settings['name']); ?>" required>
                    <div class="form-hint">The database will be created if it doesn't exist</div>
                </div>
                
                <div class="form-group">
                    <label for="db_user">Database Username</label>
                    <input type="text" id="db_user" name="db_user" value="<?php echo htmlspecialchars($db_settings['user']); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="db_pass">Database Password</label>
                    <input type="password" id="db_pass" name="db_pass" value="<?php echo htmlspecialchars($db_settings['pass']); ?>">
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn">Update Credentials</button>
                </div>
            </form>
            
        <?php elseif ($step === 2): ?>
            <div class="status-box">
                <h2>Database Connection Status</h2>
                <p><strong>Connection:</strong> <?php echo $connection_status ? 'Connected' : 'Not Connected'; ?></p>
                <p><strong>Database:</strong> <?php echo htmlspecialchars($db_settings['name']); ?></p>
                <p><strong>Tables Found:</strong> <?php echo count($tables); ?></p>
                <p><strong>Missing Tables:</strong> <?php echo count($missing_tables); ?></p>
            </div>
            
            <?php if (!empty($missing_tables)): ?>
                <h2>Fix Missing Tables</h2>
                <p>The following tables are missing from your database. Select the tables you want to create:</p>
                
                <form method="POST" action="<?php echo $_SERVER['PHP_SELF']; ?>?step=2">
                    <input type="hidden" name="fix_tables" value="1">
                    
                    <div class="checkbox-list">
                        <?php foreach ($missing_tables as $table): ?>
                            <div class="checkbox-item">
                                <label>
                                    <input type="checkbox" name="tables[]" value="<?php echo htmlspecialchars($table); ?>" checked>
                                    <?php echo htmlspecialchars($table); ?>
                                </label>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <div class="form-group" style="margin-top: 15px;">
                        <button type="submit" class="btn">Create Selected Tables</button>
                    </div>
                </form>
            <?php else: ?>
                <div class="success">
                    <p>All tables are present in the database. No fixes needed.</p>
                </div>
            <?php endif; ?>
            
            <p><a href="<?php echo $_SERVER['PHP_SELF']; ?>?step=1" class="btn" style="background-color: #6c757d;">Back to Database Settings</a></p>
        <?php endif; ?>
    </div>
</body>
</html>
