<?php
/**
 * Admin Request Payment
 *
 * This file contains the functionality for admins to request payments from users.
 *
 * @package LendSwift
 */

// Define LENDSWIFT constant to prevent direct access to included files
define('LENDSWIFT', true);

// Include initialization file
require_once '../includes/init.php';

// Include email functions
require_once '../includes/email_functions.php';

// Check if the admin is logged in
if (!is_admin_logged_in()) {
    set_flash_message('error', 'Please log in to access the admin dashboard.');
    redirect(BASE_URL . '/admin/login.php');
}

// Get admin information
$admin_id = get_current_admin_id();
$admin_name = $_SESSION['admin_name'] ?? 'Administrator';

// Get database connection
$db = getDbConnection();

// Check if application ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    set_flash_message('error', 'Application ID is required.');
    redirect(BASE_URL . '/admin/applications.php');
}

$application_id = (int)$_GET['id'];

// Get application details
$application = null;
$result = $db->query("
    SELECT
        la.*,
        u.id as user_id,
        u.name as user_name,
        u.email as user_email,
        lp.name as product_name,
        ls.name as status_name,
        c.id as currency_id,
        c.code as currency_code,
        c.symbol as currency_symbol
    FROM
        loan_applications la
    JOIN
        users u ON la.user_id = u.id
    JOIN
        loan_products lp ON la.loan_product_id = lp.id
    JOIN
        loan_statuses ls ON la.status_id = ls.id
    JOIN
        currencies c ON la.currency_id = c.id
    WHERE
        la.id = $application_id
");

if ($result && $result->num_rows > 0) {
    $application = $result->fetch_assoc();
} else {
    set_flash_message('error', 'Application not found.');
    redirect(BASE_URL . '/admin/applications.php');
}

// Enable detailed error reporting for this page
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Create a custom error handler
function customErrorHandler($errno, $errstr, $errfile, $errline) {
    $error_time = date('YmdHis');
    $error_message = "[$error_time] Error ($errno): $errstr in $errfile on line $errline";
    error_log($error_message);

    // Don't terminate script execution
    return true;
}

// Set the custom error handler
set_error_handler("customErrorHandler");

// Define payment types
$payment_types = [
    'fee' => 'Application Fee',
    'repayment' => 'Loan Repayment',
    'installment' => 'Installment Payment',
    'late_fee' => 'Late Payment Fee',
    'processing_fee' => 'Processing Fee',
    'insurance' => 'Insurance Premium',
    'tax' => 'Tax Payment',
    'service_fee' => 'Service Fee',
    'penalty_fee' => 'Penalty Fee',
    'down_payment' => 'Down Payment',
    'security_deposit' => 'Security Deposit',
    'membership_fee' => 'Membership Fee',
    'documentation_fee' => 'Documentation Fee',
    'custom' => 'Custom Payment Type',
    'other' => 'Other'
];

// Get payment methods from database
$payment_methods = [];
$default_payment_methods = [
    'bank_transfer' => 'Bank Transfer',
    'mobile_money' => 'Mobile Money',
    'cash' => 'Cash',
    'credit_card' => 'Credit Card',
    'debit_card' => 'Debit Card',
    'paypal' => 'PayPal',
    'check' => 'Check',
    'custom' => 'Custom Payment Method',
    'other' => 'Other'
];

// Check if payment_methods table exists
$result = $db->query("SHOW TABLES LIKE 'payment_methods'");
if ($result && $result->num_rows > 0) {
    // Get payment methods from database
    $result = $db->query("
        SELECT code, name, instructions
        FROM payment_methods
        WHERE is_active = 1
        ORDER BY display_order ASC, name ASC
    ");

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $payment_methods[$row['code']] = $row['name'];

            // Store instructions for later use
            if (!empty($row['instructions'])) {
                $payment_instructions[$row['code']] = $row['instructions'];
            }
        }
    } else {
        // Use default payment methods if none found in database
        $payment_methods = $default_payment_methods;
    }
} else {
    // Use default payment methods if table doesn't exist
    $payment_methods = $default_payment_methods;
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    error_log("POST request received for payment request. Application ID: $application_id");
    error_log("POST data: " . json_encode($_POST));

    // Verify CSRF token
    if (!verify_csrf_token()) {
        error_log("CSRF token verification failed");
        set_flash_message('error', 'Invalid form submission. Please try again.');
        redirect(BASE_URL . '/admin/request-payment.php?id=' . $application_id);
    }

    error_log("CSRF token verified successfully");

    // Get form data
    error_log("Extracting and validating form data");

    // Amount validation
    $amount = isset($_POST['amount']) ? (float)$_POST['amount'] : 0;
    error_log("Amount: $amount");

    // Payment type validation
    $payment_type = isset($_POST['payment_type']) ? sanitize_input($_POST['payment_type']) : '';
    error_log("Payment type: $payment_type");

    // Payment method validation
    $payment_method = isset($_POST['payment_method']) ? sanitize_input($_POST['payment_method']) : '';
    error_log("Payment method: $payment_method");

    // Other fields
    $payment_details = isset($_POST['payment_details']) ? sanitize_input($_POST['payment_details']) : '';
    $admin_notes = isset($_POST['admin_notes']) ? sanitize_input($_POST['admin_notes']) : '';

    // Initialize errors array
    $errors = [];

    // Validate amount
    if (!isset($_POST['amount']) || $_POST['amount'] === '') {
        $errors[] = 'Amount field is missing or empty.';
        error_log("Validation error: Amount field is missing or empty");
    } elseif (!is_numeric($_POST['amount'])) {
        $errors[] = 'Amount must be a valid number.';
        error_log("Validation error: Amount is not a valid number: " . $_POST['amount']);
    } elseif ($amount <= 0) {
        $errors[] = 'Amount must be greater than zero.';
        error_log("Validation error: Amount must be greater than zero: $amount");
    }

    // Validate payment type
    if (!isset($_POST['payment_type']) || $_POST['payment_type'] === '') {
        $errors[] = 'Payment type is required.';
        error_log("Validation error: Payment type is missing or empty");
    } elseif ($payment_type === 'custom') {
        $custom_payment_type = isset($_POST['custom_payment_type']) ? sanitize_input($_POST['custom_payment_type']) : '';
        error_log("Custom payment type: $custom_payment_type");

        if (empty($custom_payment_type)) {
            $errors[] = 'Custom payment type is required when "Custom Payment Type" is selected.';
            error_log("Validation error: Custom payment type is required but empty");
        } else {
            $payment_type = $custom_payment_type;
            error_log("Using custom payment type: $payment_type");
        }
    }

    // Validate payment method
    if (!isset($_POST['payment_method']) || $_POST['payment_method'] === '') {
        $errors[] = 'Payment method is required.';
        error_log("Validation error: Payment method is missing or empty");
    } elseif ($payment_method === 'custom') {
        $custom_payment_method = isset($_POST['custom_payment_method']) ? sanitize_input($_POST['custom_payment_method']) : '';
        error_log("Custom payment method: $custom_payment_method");

        if (empty($custom_payment_method)) {
            $errors[] = 'Custom payment method is required when "Custom Payment Method" is selected.';
            error_log("Validation error: Custom payment method is required but empty");
        } else {
            $payment_method = $custom_payment_method;
            error_log("Using custom payment method: $payment_method");
        }
    }

    // Log validation results
    if (empty($errors)) {
        error_log("Form validation passed successfully");
    } else {
        error_log("Form validation failed with " . count($errors) . " errors: " . implode(", ", $errors));
    }

    // If no errors, create transaction
    if (empty($errors)) {
        try {
            // Check database connection
            if ($db->connect_error) {
                throw new Exception("Database connection failed: " . $db->connect_error);
            }
            error_log("Database connection verified");

            // Start transaction for data integrity
            error_log("Starting database transaction");
            $db->begin_transaction();

            // Enable error logging
            error_log("Starting payment request process for application ID: $application_id");

            // Generate reference number
            $reference = 'PAY-' . strtoupper(substr(md5(uniqid()), 0, 8)) . '-' . $application_id;
            error_log("Generated reference number: $reference");

            // Verify application exists again before proceeding
            $verify_app = $db->prepare("SELECT id FROM loan_applications WHERE id = ?");
            if (!$verify_app) {
                throw new Exception("Error preparing application verification query: " . $db->error);
            }

            $verify_app->bind_param("i", $application_id);
            $verify_app->execute();
            $verify_result = $verify_app->get_result();

            if ($verify_result->num_rows === 0) {
                throw new Exception("Application ID $application_id not found in database");
            }
            error_log("Application verified in database");

            // Map payment types to the allowed ENUM values in the database
            // The database only allows 'payment', 'disbursement', 'fee', 'refund' in the type column
            $enum_payment_types = ['payment', 'disbursement', 'fee', 'refund'];

            // Default to using 'fee' as the type and storing the specific type in custom_type
            $db_payment_type = 'fee'; // Default to 'fee' for most payment types
            $use_custom_type = true;

            // Map specific payment types to the allowed ENUM values
            if ($payment_type === 'payment' || $payment_type === 'repayment') {
                $db_payment_type = 'payment';
                $use_custom_type = false;
            } elseif ($payment_type === 'disbursement') {
                $db_payment_type = 'disbursement';
                $use_custom_type = false;
            } elseif ($payment_type === 'refund') {
                $db_payment_type = 'refund';
                $use_custom_type = false;
            } elseif ($payment_type === 'fee') {
                $db_payment_type = 'fee';
                $use_custom_type = false;
            }

            error_log("Original payment type: '$payment_type', mapped to DB type: '$db_payment_type', use_custom_type: " . ($use_custom_type ? 'true' : 'false'));

            // Check if payment_method is one of the predefined methods or a custom one
            $valid_payment_methods = array_keys($payment_methods);
            $valid_payment_methods = array_filter($valid_payment_methods, function($method) {
                return $method !== 'custom'; // Exclude 'custom' from valid methods
            });

            // Check if the payment method is too long for the 'payment_method' column (which is VARCHAR(50))
            $payment_method_column = 'custom_payment_method';
            if (in_array($payment_method, $valid_payment_methods) && strlen($payment_method) <= 50) {
                $payment_method_column = 'payment_method';
            }

            error_log("Payment method: '$payment_method', length: " . strlen($payment_method) . ", using column: $payment_method_column");

            // Check if the custom columns exist in the transactions table
            error_log("Checking if custom columns exist in transactions table");

            try {
                $result = $db->query("SHOW COLUMNS FROM transactions LIKE 'custom_type'");
                if (!$result) {
                    throw new Exception("Error checking for custom_type column: " . $db->error);
                }

                if ($result->num_rows === 0) {
                    // Add custom_type column if it doesn't exist
                    error_log("Adding custom_type column to transactions table");
                    $alterResult = $db->query("ALTER TABLE transactions ADD COLUMN custom_type VARCHAR(100) NULL AFTER type");
                    if (!$alterResult) {
                        throw new Exception("Error adding custom_type column: " . $db->error);
                    }
                    error_log("Successfully added custom_type column");
                } else {
                    error_log("custom_type column already exists");
                }
            } catch (Exception $e) {
                error_log("Error with custom_type column: " . $e->getMessage());
                throw $e; // Re-throw to be caught by the outer try-catch
            }

            try {
                $result = $db->query("SHOW COLUMNS FROM transactions LIKE 'custom_payment_method'");
                if (!$result) {
                    throw new Exception("Error checking for custom_payment_method column: " . $db->error);
                }

                if ($result->num_rows === 0) {
                    // Add custom_payment_method column if it doesn't exist
                    error_log("Adding custom_payment_method column to transactions table");
                    $alterResult = $db->query("ALTER TABLE transactions ADD COLUMN custom_payment_method VARCHAR(100) NULL AFTER payment_method");
                    if (!$alterResult) {
                        throw new Exception("Error adding custom_payment_method column: " . $db->error);
                    }
                    error_log("Successfully added custom_payment_method column");
                } else {
                    error_log("custom_payment_method column already exists");
                }
            } catch (Exception $e) {
                error_log("Error with custom_payment_method column: " . $e->getMessage());
                throw $e; // Re-throw to be caught by the outer try-catch
            }

            // Insert transaction with appropriate columns
            error_log("Preparing to insert transaction with db_payment_type=$db_payment_type, use_custom_type=" . ($use_custom_type ? 'true' : 'false') . ", payment_method_column=$payment_method_column");

            if (!$use_custom_type && $payment_method_column === 'payment_method') {
                // Standard payment type and standard payment method
                error_log("Using standard payment type and standard payment method");

                $query = "
                    INSERT INTO transactions (
                        application_id, user_id, type, amount, currency_id,
                        transaction_date, payment_method, reference_number, status,
                        description, created_at
                    ) VALUES (
                        ?, ?, ?, ?, ?, NOW(), ?, ?, 'pending', ?, NOW()
                    )
                ";

                error_log("Preparing query: $query");
                $stmt = $db->prepare($query);

                if (!$stmt) {
                    throw new Exception("Error preparing statement: " . $db->error);
                }

                error_log("Binding parameters: application_id=$application_id, user_id={$application['user_id']}, db_payment_type=$db_payment_type, amount=$amount, currency_id={$application['currency_id']}, payment_method=$payment_method, reference_number=$reference, admin_notes=*****");

                $bind_result = $stmt->bind_param("iisdisss",
                    $application_id,
                    $application['user_id'],
                    $db_payment_type,
                    $amount,
                    $application['currency_id'],
                    $payment_method,
                    $reference,
                    $admin_notes
                );

                if (!$bind_result) {
                    throw new Exception("Error binding parameters: " . $stmt->error);
                }
            } elseif ($use_custom_type && $payment_method_column === 'payment_method') {
                // Custom payment type, standard payment method
                error_log("Using custom payment type and standard payment method");

                $query = "
                    INSERT INTO transactions (
                        application_id, user_id, type, custom_type, amount, currency_id,
                        transaction_date, payment_method, reference_number, status,
                        description, created_at
                    ) VALUES (
                        ?, ?, ?, ?, ?, ?, NOW(), ?, ?, 'pending', ?, NOW()
                    )
                ";

                // Verify parameter count: 9 placeholders in the query should match 9 parameters in bind_param

                error_log("Preparing query: $query");
                $stmt = $db->prepare($query);

                if (!$stmt) {
                    throw new Exception("Error preparing statement for custom type: " . $db->error);
                }

                // Count placeholders in the query: 9 (?, ?, ?, ?, ?, ?, NOW(), ?, ?, 'pending', ?, NOW())
                // Count parameters in bind_param: 9 (i, i, s, s, d, i, s, s, s)
                error_log("Binding parameters with custom payment type: application_id=$application_id, user_id={$application['user_id']}, db_payment_type=$db_payment_type, custom_type=$payment_type, amount=$amount, currency_id={$application['currency_id']}, payment_method=$payment_method, reference_number=$reference, admin_notes=*****");

                // The correct number of parameters is 9
                $bind_result = $stmt->bind_param("iissdisss",
                    $application_id,
                    $application['user_id'],
                    $db_payment_type,
                    $payment_type,
                    $amount,
                    $application['currency_id'],
                    $payment_method,
                    $reference,
                    $admin_notes
                );

                if (!$bind_result) {
                    throw new Exception("Error binding parameters for custom type: " . $stmt->error);
                }
            } elseif (!$use_custom_type && $payment_method_column === 'custom_payment_method') {
                // Standard payment type, custom payment method
                error_log("Using standard payment type and custom payment method");

                $query = "
                    INSERT INTO transactions (
                        application_id, user_id, type, amount, currency_id,
                        transaction_date, payment_method, custom_payment_method, reference_number, status,
                        description, created_at
                    ) VALUES (
                        ?, ?, ?, ?, ?, NOW(), 'other', ?, ?, 'pending', ?, NOW()
                    )
                ";

                error_log("Preparing query: $query");
                $stmt = $db->prepare($query);

                if (!$stmt) {
                    throw new Exception("Error preparing statement for custom payment method: " . $db->error);
                }

                error_log("Binding parameters with custom payment method: application_id=$application_id, user_id={$application['user_id']}, db_payment_type=$db_payment_type, amount=$amount, currency_id={$application['currency_id']}, custom_payment_method=$payment_method, reference_number=$reference, admin_notes=*****");

                $bind_result = $stmt->bind_param("iisdisss",
                    $application_id,
                    $application['user_id'],
                    $db_payment_type,
                    $amount,
                    $application['currency_id'],
                    $payment_method,
                    $reference,
                    $admin_notes
                );

                if (!$bind_result) {
                    throw new Exception("Error binding parameters for custom payment method: " . $stmt->error);
                }
            } else {
                // Both are custom
                error_log("Using both custom payment type and custom payment method");

                $query = "
                    INSERT INTO transactions (
                        application_id, user_id, type, custom_type, amount, currency_id,
                        transaction_date, payment_method, custom_payment_method, reference_number, status,
                        description, created_at
                    ) VALUES (
                        ?, ?, ?, ?, ?, ?, NOW(), 'other', ?, ?, 'pending', ?, NOW()
                    )
                ";

                // Count placeholders in the query: 9 (?, ?, ?, ?, ?, ?, NOW(), 'other', ?, ?, 'pending', ?, NOW())

                error_log("Preparing query: $query");
                $stmt = $db->prepare($query);

                if (!$stmt) {
                    throw new Exception("Error preparing statement for both custom: " . $db->error);
                }

                // Count placeholders in the query: 9 (?, ?, ?, ?, ?, ?, NOW(), 'other', ?, ?, 'pending', ?, NOW())
                // Count parameters in bind_param: 9 (i, i, s, s, d, i, s, s, s)
                error_log("Binding parameters with both custom: application_id=$application_id, user_id={$application['user_id']}, db_payment_type=$db_payment_type, custom_type=$payment_type, amount=$amount, currency_id={$application['currency_id']}, custom_payment_method=$payment_method, reference_number=$reference, admin_notes=*****");

                $bind_result = $stmt->bind_param("iissdsss",
                    $application_id,
                    $application['user_id'],
                    $db_payment_type,
                    $payment_type,
                    $amount,
                    $application['currency_id'],
                    $payment_method,
                    $reference,
                    $admin_notes
                );

                if (!$bind_result) {
                    throw new Exception("Error binding parameters for both custom: " . $stmt->error);
                }
            }

            error_log("Executing statement");
            $execute_result = $stmt->execute();

            if (!$execute_result) {
                throw new Exception("Error executing statement: " . $stmt->error);
            }

            error_log("Statement executed successfully");
            $transaction_id = $db->insert_id;
            error_log("Transaction ID: $transaction_id");

            // Send email notification to user
            $subject = "Payment Request for Loan Application #" . $application_id;

            $message = "
            <p>Dear " . htmlspecialchars($application['user_name']) . ",</p>

            <p>A payment request has been created for your loan application #" . $application_id . " for " . htmlspecialchars($application['product_name']) . ".</p>

            <p><strong>Payment Details:</strong></p>
            <ul>
                <li><strong>Amount:</strong> " . $application['currency_symbol'] . number_format($amount, 2) . "</li>
                <li><strong>Payment Type:</strong> " . ucfirst(str_replace('_', ' ', $payment_type)) . "</li>
                <li><strong>Payment Method:</strong> " . ucfirst(str_replace('_', ' ', htmlspecialchars($payment_method))) . "</li>
                <li><strong>Reference Number:</strong> " . $reference . "</li>
            </ul>

            <p><strong>Payment Instructions:</strong></p>
            <p>" . nl2br(htmlspecialchars($payment_details)) . "</p>
            ";

            if (!empty($admin_notes)) {
                $message .= "<p><strong>Additional Notes:</strong> " . nl2br(htmlspecialchars($admin_notes)) . "</p>";
            }

            $message .= "
            <p>Please log in to your account to upload your payment receipt once you have made the payment.</p>

            <p>Thank you for your cooperation.</p>

            <p>Best regards,<br>
            The LendSwift Team</p>
            ";

            error_log("Preparing to send email notification to: " . $application['user_email']);

            try {
                $email_sent = send_notification_email($application['user_email'], $subject, $message);

                // Check if notifications table has title column
                $result = $db->query("SHOW COLUMNS FROM notifications LIKE 'title'");
                $has_title_column = $result && $result->num_rows > 0;

                if (!$has_title_column) {
                    // Add title column if it doesn't exist
                    try {
                        $db->query("ALTER TABLE notifications ADD COLUMN title VARCHAR(255) NULL AFTER user_id");
                        error_log("Added title column to notifications table");
                    } catch (Exception $e) {
                        error_log("Error adding title column to notifications table: " . $e->getMessage());
                        // Continue anyway, we'll handle the missing column below
                    }
                }

                // Check if notifications table has link column
                $result = $db->query("SHOW COLUMNS FROM notifications LIKE 'link'");
                $has_link_column = $result && $result->num_rows > 0;

                if (!$has_link_column) {
                    // Add link column if it doesn't exist
                    try {
                        $db->query("ALTER TABLE notifications ADD COLUMN link VARCHAR(255) NULL AFTER type");
                        error_log("Added link column to notifications table");
                    } catch (Exception $e) {
                        error_log("Error adding link column to notifications table: " . $e->getMessage());
                        // Continue anyway, we'll handle the missing column below
                    }
                }

                // Check if notifications table has related_id column
                $result = $db->query("SHOW COLUMNS FROM notifications LIKE 'related_id'");
                $has_related_id_column = $result && $result->num_rows > 0;

                if (!$has_related_id_column) {
                    // Add related_id column if it doesn't exist
                    try {
                        $db->query("ALTER TABLE notifications ADD COLUMN related_id INT NULL AFTER link");
                        error_log("Added related_id column to notifications table");
                    } catch (Exception $e) {
                        error_log("Error adding related_id column to notifications table: " . $e->getMessage());
                        // Continue anyway, we'll handle the missing column below
                    }
                }

                // Check if notifications table has related_type column
                $result = $db->query("SHOW COLUMNS FROM notifications LIKE 'related_type'");
                $has_related_type_column = $result && $result->num_rows > 0;

                if (!$has_related_type_column) {
                    // Add related_type column if it doesn't exist
                    try {
                        $db->query("ALTER TABLE notifications ADD COLUMN related_type VARCHAR(50) NULL AFTER related_id");
                        error_log("Added related_type column to notifications table");
                    } catch (Exception $e) {
                        error_log("Error adding related_type column to notifications table: " . $e->getMessage());
                        // Continue anyway, we'll handle the missing column below
                    }
                }

                // Create in-app notification for the user
                $notification_title = "Payment Request";
                $notification_message = "A payment of " . $application['currency_symbol'] . number_format($amount, 2) . " has been requested for your loan application #" . $application_id;
                $notification_link = "/?page=transaction-details&id=" . $transaction_id;

                try {
                    create_notification($application['user_id'], $notification_title, $notification_message, 'info', $notification_link, $transaction_id, 'transaction');
                    error_log("Notification created successfully");
                } catch (Exception $e) {
                    error_log("Error creating notification: " . $e->getMessage());
                    // Continue execution, this is not a critical error
                }

                if ($email_sent) {
                    error_log("Email notification sent successfully");
                    set_flash_message('success', 'Payment request created successfully and notification email sent to applicant.');
                } else {
                    error_log("Failed to send email notification");
                    set_flash_message('success', 'Payment request created successfully but failed to send notification email.');
                }
            } catch (Exception $e) {
                error_log("Error sending email notification: " . $e->getMessage());
                set_flash_message('success', 'Payment request created successfully but there was an error sending the notification email: ' . $e->getMessage());
            }

            // Commit the transaction
            error_log("Committing database transaction");
            $db->commit();

            error_log("Redirecting to application details page");
            redirect(BASE_URL . '/admin/application-details.php?id=' . $application_id);
        } catch (Exception $e) {
            // Rollback the transaction if it was started
            if ($db->connect_error === null && method_exists($db, 'rollback')) {
                error_log("Rolling back database transaction due to error");
                try {
                    $db->rollback();
                    error_log("Transaction rollback successful");
                } catch (Exception $rollback_error) {
                    error_log("Transaction rollback failed: " . $rollback_error->getMessage());
                }
            }

            $error_message = $e->getMessage();
            $error_trace = $e->getTraceAsString();
            $error_code = $e->getCode();
            $error_file = $e->getFile();
            $error_line = $e->getLine();
            $error_time = date('YmdHis');

            // Create a detailed error log
            error_log("==================== PAYMENT REQUEST ERROR ====================");
            error_log("Error Time: $error_time");
            error_log("Error Message: $error_message");
            error_log("Error Code: $error_code");
            error_log("Error File: $error_file");
            error_log("Error Line: $error_line");
            error_log("Error Trace: $error_trace");

            // Log database-specific errors if available
            if (isset($stmt) && $stmt instanceof mysqli_stmt) {
                error_log("Statement Error: " . $stmt->error);
                error_log("Statement Error Code: " . $stmt->errno);
            }

            if ($db instanceof mysqli) {
                error_log("Database Error: " . $db->error);
                error_log("Database Error Code: " . $db->errno);
            }

            // Check if we're in development mode
            $is_dev_mode = defined('ENVIRONMENT') && ENVIRONMENT === 'development';

            if ($is_dev_mode) {
                // In development mode, show detailed error
                set_flash_message('error', 'Error: ' . $error_message . ' in ' . $error_file . ' on line ' . $error_line . '<br><br>Please check the error log for more details. Reference: ' . $error_time);
            } else {
                // In production, show generic message but log detailed error
                set_flash_message('error', 'An error occurred while processing your payment request. Please try again later or contact support with reference: ' . $error_time);
            }

            // Log additional information that might help diagnose the issue
            error_log("Request Method: " . $_SERVER['REQUEST_METHOD']);
            error_log("Request URI: " . $_SERVER['REQUEST_URI']);
            error_log("User Agent: " . $_SERVER['HTTP_USER_AGENT']);
            error_log("IP Address: " . $_SERVER['REMOTE_ADDR']);
            error_log("POST Data: " . json_encode($_POST));
            error_log("GET Data: " . json_encode($_GET));
            error_log("Application ID: " . $application_id);
            error_log("Admin ID: " . $admin_id);
            error_log("==================== END ERROR REPORT ====================");

            // Display a more user-friendly error message
            set_flash_message('error', 'An error occurred while processing your payment request. Please try again or contact support if the problem persists. Error details: ' . $e->getMessage());
        }
    } else {
        // Set error messages
        if (count($errors) === 1) {
            set_flash_message('error', $errors[0]);
        } else {
            $error_list = '<ul style="margin-top: 5px; margin-bottom: 5px;">';
            foreach ($errors as $error) {
                $error_list .= '<li>' . htmlspecialchars($error) . '</li>';
            }
            $error_list .= '</ul>';
            set_flash_message('error', 'Please correct the following errors:' . $error_list);
        }
    }
}

// Include admin header
include '../includes/admin_header.php';
?>

<div class="request-payment">
    <div class="page-header">
        <h1>Request Payment</h1>
        <div class="page-actions">
            <a href="<?php echo BASE_URL; ?>/admin/application-details.php?id=<?php echo $application_id; ?>" class="button button-secondary">
                <i class="fas fa-arrow-left"></i> Back to Application
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2>Request Payment for <?php echo htmlspecialchars($application['user_name']); ?></h2>
        </div>
        <div class="card-content">
            <div class="application-info">
                <div class="info-item">
                    <span class="info-label">Application ID:</span>
                    <span class="info-value"><?php echo $application_id; ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">User:</span>
                    <span class="info-value"><?php echo htmlspecialchars($application['user_name']); ?> (<?php echo htmlspecialchars($application['user_email']); ?>)</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Product:</span>
                    <span class="info-value"><?php echo htmlspecialchars($application['product_name']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Amount:</span>
                    <span class="info-value"><?php echo $application['currency_symbol'] . number_format($application['applied_amount'], 2); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Status:</span>
                    <span class="info-value">
                        <span class="status-badge status-<?php echo strtolower(str_replace(' ', '-', $application['status_name'])); ?>">
                            <?php echo htmlspecialchars($application['status_name']); ?>
                        </span>
                    </span>
                </div>
            </div>

            <form id="paymentRequestForm" method="POST" action="<?php echo BASE_URL; ?>/admin/request-payment.php?id=<?php echo $application_id; ?>">
                <?php echo csrf_token_field(); ?>

                <?php if (defined('ENVIRONMENT') && ENVIRONMENT === 'development'): ?>
                <div class="alert alert-info">
                    <strong>Development Mode</strong> - Detailed error information will be shown.
                    <a href="#" id="debugToggle" class="button button-small">Toggle Debug Info</a>
                </div>

                <div id="debugInfo" class="debug-info">
                    <h4>Debug Information</h4>
                    <p>Application ID: <?php echo $application_id; ?></p>
                    <p>Admin ID: <?php echo $admin_id; ?></p>
                    <p>PHP Version: <?php echo phpversion(); ?></p>
                    <p>MySQL Version: <?php echo $db->server_info; ?></p>
                    <p>Server: <?php echo $_SERVER['SERVER_SOFTWARE']; ?></p>
                </div>
                <?php endif; ?>

                <div class="form-group">
                    <label for="amount">Amount <span class="required">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text"><?php echo $application['currency_symbol']; ?></span>
                        <input type="number" id="amount" name="amount" class="form-control" value="<?php echo htmlspecialchars($_POST['amount'] ?? ''); ?>" step="0.01" min="0.01" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="payment_type">Payment Type <span class="required">*</span></label>
                    <div class="select-wrapper">
                        <select id="payment_type" name="payment_type" class="form-control styled-select" required onchange="toggleCustomPaymentType()">
                            <option value="">Select Payment Type</option>
                            <?php foreach ($payment_types as $value => $label): ?>
                                <option value="<?php echo $value; ?>" <?php echo ($_POST['payment_type'] ?? '') === $value ? 'selected' : ''; ?>><?php echo htmlspecialchars($label); ?></option>
                            <?php endforeach; ?>
                        </select>
                        <div class="select-arrow">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                <path d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z"/>
                            </svg>
                        </div>
                    </div>
                </div>

                <div id="custom_payment_type_container" class="form-group" style="display: none;">
                    <label for="custom_payment_type">Custom Payment Type <span class="required">*</span></label>
                    <input type="text" id="custom_payment_type" name="custom_payment_type" class="form-control" value="<?php echo htmlspecialchars($_POST['custom_payment_type'] ?? ''); ?>" placeholder="Enter custom payment type">
                    <p class="field-hint">Specify your custom payment type (e.g., "Membership Fee", "Documentation Fee", etc.)</p>
                </div>

                <div class="form-group">
                    <label for="payment_method">Payment Method <span class="required">*</span></label>
                    <div class="select-wrapper">
                        <select id="payment_method" name="payment_method" class="form-control styled-select" required onchange="toggleCustomPaymentMethod()">
                            <option value="">Select Payment Method</option>
                            <?php foreach ($payment_methods as $value => $label): ?>
                                <option value="<?php echo $value; ?>" <?php echo ($_POST['payment_method'] ?? '') === $value ? 'selected' : ''; ?>><?php echo htmlspecialchars($label); ?></option>
                            <?php endforeach; ?>
                        </select>
                        <div class="select-arrow">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                <path d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z"/>
                            </svg>
                        </div>
                    </div>
                </div>

                <div id="custom_payment_method_container" class="form-group" style="display: none;">
                    <label for="custom_payment_method">Custom Payment Method <span class="required">*</span></label>
                    <input type="text" id="custom_payment_method" name="custom_payment_method" class="form-control" value="<?php echo htmlspecialchars($_POST['custom_payment_method'] ?? ''); ?>" placeholder="Enter custom payment method">
                    <p class="field-hint">Specify your custom payment method (e.g., "Western Union", "Direct Deposit", etc.)</p>
                </div>

                <div class="form-group">
                    <label for="payment_details">Payment Details <span class="required">*</span></label>
                    <textarea id="payment_details" name="payment_details" class="form-control" rows="5" required><?php echo htmlspecialchars($_POST['payment_details'] ?? ''); ?></textarea>
                    <p class="field-hint">Provide detailed instructions for the payment, including account numbers, recipient name, etc.</p>
                    <div class="payment-instructions-container" style="display: none;">
                        <div class="payment-instructions-box">
                            <div class="payment-instructions-header">
                                <h4>Default Instructions</h4>
                                <button type="button" class="use-instructions-btn" onclick="useDefaultInstructions()">Use These Instructions</button>
                            </div>
                            <div id="payment-instructions-content" class="payment-instructions-content"></div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="admin_notes">Admin Notes</label>
                    <textarea id="admin_notes" name="admin_notes" class="form-control" rows="3"><?php echo htmlspecialchars($_POST['admin_notes'] ?? ''); ?></textarea>
                    <p class="field-hint">These notes will be visible to the user.</p>
                </div>

                <div class="form-actions">
                    <button type="submit" class="button button-primary">
                        <i class="fas fa-paper-plane"></i> Send Payment Request
                    </button>
                    <a href="<?php echo BASE_URL; ?>/admin/application-details.php?id=<?php echo $application_id; ?>" class="button button-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
    .request-payment {
        margin-bottom: 2rem;
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }

    .card {
        background-color: #fff;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .card-header {
        padding: 1.5rem;
        border-bottom: 1px solid #e5e7eb;
    }

    .card-header h2 {
        margin: 0;
        font-size: 1.25rem;
    }

    .card-content {
        padding: 1.5rem;
    }

    .application-info {
        background-color: #f9fafb;
        border-radius: 0.375rem;
        padding: 1rem;
        margin-bottom: 1.5rem;
        border: 1px solid #e5e7eb;
    }

    .info-item {
        margin-bottom: 0.5rem;
        display: flex;
    }

    .info-item:last-child {
        margin-bottom: 0;
    }

    .info-label {
        font-weight: 600;
        width: 120px;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    .form-control {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        font-size: 0.95rem;
    }

    .form-control:focus {
        outline: none;
        border-color: #4f46e5;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }

    .input-group {
        display: flex;
        align-items: stretch;
    }

    .input-group-text {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        background-color: #f3f4f6;
        border: 1px solid #d1d5db;
        border-right: none;
        border-radius: 0.5rem 0 0 0.5rem;
        font-weight: 500;
    }

    .input-group .form-control {
        border-radius: 0 0.5rem 0.5rem 0;
    }

    .field-hint {
        margin-top: 0.5rem;
        font-size: 0.875rem;
        color: #6b7280;
    }

    .required {
        color: #ef4444;
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        margin-top: 2rem;
    }

    .button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 1.5rem;
        font-size: 0.95rem;
        font-weight: 500;
        border-radius: 0.5rem;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .button i {
        margin-right: 0.5rem;
    }

    .button-primary {
        background-color: #4f46e5;
        color: #fff;
    }

    .button-primary:hover {
        background-color: #4338ca;
    }

    .button-secondary {
        background-color: #f3f4f6;
        color: #374151;
        border: 1px solid #d1d5db;
    }

    .button-secondary:hover {
        background-color: #e5e7eb;
    }

    /* Styled Select */
    .select-wrapper {
        position: relative;
        width: 100%;
    }

    .styled-select {
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        width: 100%;
        padding: 0.75rem 1rem;
        font-size: 0.95rem;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        background-color: #fff;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .styled-select:focus {
        outline: none;
        border-color: #4f46e5;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }

    .select-arrow {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
        color: #6b7280;
    }

    .status-badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        font-weight: 600;
        border-radius: 0.25rem;
    }

    .status-pending {
        background-color: #fef3c7;
        color: #92400e;
    }

    .status-approved {
        background-color: #d1fae5;
        color: #065f46;
    }

    .status-rejected {
        background-color: #fee2e2;
        color: #b91c1c;
    }

    .status-pending-review {
        background-color: #fef3c7;
        color: #92400e;
    }

    .status-under-assessment {
        background-color: #e0e7ff;
        color: #3730a3;
    }

    .status-disbursed {
        background-color: #dbeafe;
        color: #1e40af;
    }

    .status-closed {
        background-color: #e5e7eb;
        color: #374151;
    }

    .error-message {
        color: #dc2626;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .form-group.has-error .form-control {
        border-color: #dc2626;
    }

    .field-hint {
        color: #6b7280;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .payment-instructions-container {
        margin-top: 1rem;
    }

    .payment-instructions-box {
        background-color: #f3f4f6;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        padding: 1rem;
    }

    .payment-instructions-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;
    }

    .payment-instructions-header h4 {
        margin: 0;
        font-size: 0.95rem;
        color: #4b5563;
    }

    .use-instructions-btn {
        background-color: #4f46e5;
        color: white;
        border: none;
        border-radius: 0.375rem;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .use-instructions-btn:hover {
        background-color: #4338ca;
    }

    .payment-instructions-content {
        white-space: pre-line;
        font-size: 0.875rem;
        color: #4b5563;
        line-height: 1.5;
    }

    /* Improved form validation styling */
    .form-control.is-invalid {
        border-color: #dc2626;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc2626' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc2626' stroke='none'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    }

    .invalid-feedback {
        display: none;
        width: 100%;
        margin-top: 0.25rem;
        font-size: 0.875rem;
        color: #dc2626;
    }

    .form-control.is-invalid ~ .invalid-feedback {
        display: block;
    }

    .debug-info {
        background-color: #f3f4f6;
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        padding: 1rem;
        margin-top: 1.5rem;
        font-family: monospace;
        font-size: 0.875rem;
        white-space: pre-wrap;
        display: none;
    }
</style>

<script>
    // Function to toggle custom payment type field
    function toggleCustomPaymentType() {
        const paymentTypeSelect = document.getElementById('payment_type');
        const customPaymentTypeContainer = document.getElementById('custom_payment_type_container');
        const customPaymentTypeInput = document.getElementById('custom_payment_type');

        if (paymentTypeSelect.value === 'custom') {
            customPaymentTypeContainer.style.display = 'block';
            customPaymentTypeInput.setAttribute('required', 'required');
        } else {
            customPaymentTypeContainer.style.display = 'none';
            customPaymentTypeInput.removeAttribute('required');
        }
    }

    // Function to toggle custom payment method field
    function toggleCustomPaymentMethod() {
        const paymentMethodSelect = document.getElementById('payment_method');
        const customPaymentMethodContainer = document.getElementById('custom_payment_method_container');
        const customPaymentMethodInput = document.getElementById('custom_payment_method');
        const instructionsContainer = document.querySelector('.payment-instructions-container');
        const instructionsContent = document.getElementById('payment-instructions-content');

        if (paymentMethodSelect.value === 'custom') {
            customPaymentMethodContainer.style.display = 'block';
            customPaymentMethodInput.setAttribute('required', 'required');
            instructionsContainer.style.display = 'none';
        } else {
            customPaymentMethodContainer.style.display = 'none';
            customPaymentMethodInput.removeAttribute('required');

            // Check if we have instructions for this payment method
            const selectedMethod = paymentMethodSelect.value;
            const instructions = getPaymentInstructions(selectedMethod);

            if (instructions) {
                instructionsContent.textContent = instructions;
                instructionsContainer.style.display = 'block';
            } else {
                instructionsContainer.style.display = 'none';
            }
        }
    }

    // Function to get payment instructions for a method
    function getPaymentInstructions(methodCode) {
        // Define payment instructions
        const paymentInstructions = <?php echo json_encode($payment_instructions ?? []); ?>;
        return paymentInstructions[methodCode] || null;
    }

    // Function to use default instructions
    function useDefaultInstructions() {
        const paymentDetailsField = document.getElementById('payment_details');
        const instructionsContent = document.getElementById('payment-instructions-content');

        if (instructionsContent.textContent) {
            paymentDetailsField.value = instructionsContent.textContent;
        }
    }

    // Function to show validation error with Bootstrap styling
    function showValidationError(element, message) {
        // Add invalid class to the input
        element.classList.add('is-invalid');

        // Create feedback element if it doesn't exist
        const formGroup = element.closest('.form-group');
        let feedback = formGroup.querySelector('.invalid-feedback');

        if (!feedback) {
            feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';

            // Insert after the input or after the select-wrapper if it exists
            const selectWrapper = formGroup.querySelector('.select-wrapper');
            if (selectWrapper && selectWrapper.contains(element)) {
                selectWrapper.after(feedback);
            } else {
                element.after(feedback);
            }
        }

        feedback.textContent = message;

        // Log error to console for debugging
        console.error(`Validation error: ${message} for element ${element.id}`);
    }

    // Function to validate the form
    function validateForm(form) {
        let isValid = true;

        // Get form elements
        const amountInput = form.querySelector('#amount');
        const paymentTypeSelect = form.querySelector('#payment_type');
        const customPaymentTypeInput = form.querySelector('#custom_payment_type');
        const paymentMethodSelect = form.querySelector('#payment_method');
        const customPaymentMethodInput = form.querySelector('#custom_payment_method');
        const paymentDetailsInput = form.querySelector('#payment_details');

        // Clear all previous errors
        form.querySelectorAll('.form-group').forEach(group => {
            const input = group.querySelector('input, select, textarea');
            if (input) {
                input.classList.remove('is-invalid');
                const feedback = group.querySelector('.invalid-feedback');
                if (feedback) {
                    feedback.remove();
                }
            }
        });

        // Validate amount
        if (!amountInput.value.trim()) {
            showValidationError(amountInput, 'Amount is required');
            isValid = false;
        } else if (isNaN(amountInput.value) || parseFloat(amountInput.value) <= 0) {
            showValidationError(amountInput, 'Amount must be a positive number');
            isValid = false;
        }

        // Validate payment type
        if (!paymentTypeSelect.value) {
            showValidationError(paymentTypeSelect, 'Payment type is required');
            isValid = false;
        } else if (paymentTypeSelect.value === 'custom' && !customPaymentTypeInput.value.trim()) {
            showValidationError(customPaymentTypeInput, 'Custom payment type is required');
            isValid = false;
        }

        // Validate payment method
        if (!paymentMethodSelect.value) {
            showValidationError(paymentMethodSelect, 'Payment method is required');
            isValid = false;
        } else if (paymentMethodSelect.value === 'custom' && !customPaymentMethodInput.value.trim()) {
            showValidationError(customPaymentMethodInput, 'Custom payment method is required');
            isValid = false;
        }

        // Validate payment details
        if (!paymentDetailsInput.value.trim()) {
            showValidationError(paymentDetailsInput, 'Payment details are required');
            isValid = false;
        }

        // If validation fails, prevent form submission
        if (!isValid) {
            console.error('Form validation failed');

            // Show validation summary at the top of the form
            const formTop = form.querySelector('.card-content');
            const existingSummary = form.querySelector('.validation-summary');

            if (existingSummary) {
                existingSummary.remove();
            }

            const validationSummary = document.createElement('div');
            validationSummary.className = 'validation-summary alert alert-danger';
            validationSummary.innerHTML = '<strong>Please correct the following errors:</strong><ul>' +
                Array.from(form.querySelectorAll('.error-message'))
                    .map(el => `<li>${el.textContent}</li>`)
                    .join('') +
                '</ul>';

            formTop.insertBefore(validationSummary, formTop.firstChild);
        }

        return isValid;
    }

    // Initialize the form on page load
    document.addEventListener('DOMContentLoaded', function() {
        toggleCustomPaymentType();
        toggleCustomPaymentMethod();

        // Add event listener for payment method changes
        const paymentMethodSelect = document.getElementById('payment_method');
        if (paymentMethodSelect) {
            paymentMethodSelect.addEventListener('change', toggleCustomPaymentMethod);
        }

        // Add form validation
        const paymentForm = document.getElementById('paymentRequestForm');
        if (paymentForm) {
            paymentForm.addEventListener('submit', function(event) {
                if (!validateForm(this)) {
                    event.preventDefault();
                }
            });

            // Add input event listeners to clear errors on input
            paymentForm.querySelectorAll('input, select').forEach(input => {
                input.addEventListener('input', function() {
                    // Remove validation styling
                    this.classList.remove('is-invalid');
                    const feedback = this.closest('.form-group').querySelector('.invalid-feedback');
                    if (feedback) {
                        feedback.remove();
                    }
                });
            });
        }

        // Add debug mode toggle (only in development)
        const debugToggle = document.getElementById('debugToggle');
        if (debugToggle) {
            debugToggle.addEventListener('click', function(event) {
                event.preventDefault();
                const debugInfo = document.getElementById('debugInfo');
                if (debugInfo) {
                    debugInfo.style.display = debugInfo.style.display === 'none' ? 'block' : 'none';
                }
            });
        }
    });
</script>

<?php
// Include admin footer
include '../includes/admin_footer.php';
?>
